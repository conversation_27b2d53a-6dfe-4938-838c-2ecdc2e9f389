<template>
  <el-container class="common-bg w-full h-full !flex-col app-layout">
    <!-- 顶部导航 -->
    <BGHead class="flex-none" />

    <!-- 路由页面 -->
    <router-view v-slot="{ Component }">
      <transition name="fade-transform" mode="out-in">
        <!-- 使用 keep-alive 缓存路由组件，确保组件状态保持 -->
        <keep-alive>
          <component :is="Component" />
        </keep-alive>
      </transition>
    </router-view>

    <!-- 紧急报警弹窗 -->
    <bf-emergency-dialog
      v-for="item in bc18AlarmDialogs"
      :key="'bc18' + item.name"
      :ref="'bc18' + item.name"
      :name="item.name"
      :device="item.device"
      :cmdTime="item.cmdTime"
      :dbRid="item.dbRid"
    />
  </el-container>
</template>

<script setup>
  import '@/modules/dataManager'
  import BGHead from '@/layouts/BFHead.vue'
  import { DispatchRouteName } from '@/router'
  import { onBeforeMount } from 'vue'
  window.bfglob.currentPlatform = DispatchRouteName
  import bfprocess from '@/utils/bfprocess.js'
  import { defineAsyncComponent, ref, onMounted } from 'vue'

  // 异步组件
  const bfEmergencyDialog = defineAsyncComponent(() => import('@/components/secondary/emergencyDialog.vue'))

  // 响应式数据
  const bc18AlarmDialogs = ref([])

  // 方法
  const load_dialog_of_bcxx = (target, ref_str, source) => {
    if (window[source.name]) {
      // 更新对话框
    } else {
      // 创建对话框
      window[source.name] = Date.now()
      target.value.push(source)
    }
  }

  const destroyDialog_of_bcxx = (target, name) => {
    for (const i in target.value) {
      const item = target.value[i]
      if (item.name === name) {
        window[name] = null
        target.value.splice(i, 1)
        break
      }
    }
  }

  // 生命周期
  onMounted(() => {
    // 订阅bc18紧急报警弹框消息
    bfglob.on('show_alarm_dialog', (device, cmdTime, dbRid) => {
      const _dialog = {
        name: device.rid,
        device: device,
        cmdTime: cmdTime,
        dbRid: dbRid,
      }
      load_dialog_of_bcxx(bc18AlarmDialogs, 'bc18', _dialog)
    })

    bfglob.on('alarm_destroyDialog', name => {
      destroyDialog_of_bcxx(bc18AlarmDialogs, name)
    })
  })

  bfprocess.loginedAfterFunc()
  // qWebChannel.initServer()
  onBeforeMount(() => {
    window.bfglob.treeLoaded = true
  })
</script>

<style scoped lang="scss"></style>
