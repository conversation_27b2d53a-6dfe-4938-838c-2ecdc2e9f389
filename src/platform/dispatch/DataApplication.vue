<template>
  <el-container class="dispatch-command-function w-full h-full flex-auto gap-[45px] max-h-[100vh_-_146.5px] overflow-y-auto">
    <bf-dispatch-data-nav class="w-[292px]" />

    <el-main class="page-container !pr-[38px] !pb-[47px] !p-0 !flex flex-col gap-[5px]">
      <page-header>
        <div v-if="childRoutes.length > 0" :key="route.matched[2]?.name" class="flex items-center gap-4 z-[100] ml-[50px]">
          <el-tag
            v-for="child in childRoutes"
            :key="child.name"
            class="!border-none text-white text-[14px] font-medium cursor-pointer"
            :style="getTagStyle(child)"
            @click="handleChildRouteClick(child)"
          >
            {{ getChildRouteLabel(child) }}
          </el-tag>
        </div>
      </page-header>

      <router-view v-slot="{ Component }">
        <transition name="fade-transform" mode="out-in">
          <!-- 使用 keep-alive 缓存路由组件，确保组件状态保持 -->
          <keep-alive>
            <div class="w-full h-full">
              <component :is="Component" />
            </div>
          </keep-alive>
        </transition>
      </router-view>
    </el-main>
  </el-container>
</template>

<script setup lang="ts">
  import BfDispatchDataNav from '@/layouts/BfDispatchDataNav.vue'
  import PageHeader from '@/layouts/PageHeader.vue'
  import { useRoute, useRouter, type RouteRecordRaw } from 'vue-router'
  import { computed } from 'vue'
  import { useI18n } from 'vue-i18n'

  const route = useRoute()
  const router = useRouter()
  const { t } = useI18n()

  const currentParentRoute = computed(() => {
    return route.matched[2]?.name as string
  })
  // 获取当前路由的子路由
  const childRoutes = computed(() => {
    // 获取当前三级路由名称（DataApplication）
    if (!currentParentRoute.value) return []

    // 从路由配置中获取子路由
    const parentRouteConfig = router.getRoutes().find(r => r.path === `/dispatch/DataApplication/${currentParentRoute.value}`)?.children
    return parentRouteConfig || []
  })

  // 子路由名称映射
  const childRouteLabels = computed(() => ({
    rfidBatteryAlarm: t('nav.activePatrolPointAlarm'),
    InspectionHistory: t('nav.InspectionHistory'),
    shiftHistory: t('nav.readerCardHistory'),
    InsRulesHistory: t('dialog.insRules'),
  }))

  // 获取子路由的显示标签
  const getChildRouteLabel = (child: RouteRecordRaw) => {
    return childRouteLabels.value[child.name as string] || child.name
  }

  // 获取标签样式
  const getTagStyle = (child: RouteRecordRaw) => {
    const isActive = route.name === child.name

    if (isActive) {
      // 激活状态的橙色渐变
      return 'background: linear-gradient(90deg, rgba(253, 161, 19, 0.0001) 0%, #fda215 50.16%, rgba(254, 159, 15, 0.0001) 100%)'
    } else {
      // 非激活状态的蓝色渐变
      return 'background: linear-gradient(90deg, rgba(20, 186, 255, 0.00006) 0%, rgba(20, 186, 255, 0.6) 50.16%, rgba(20, 186, 255, 0.00006) 100%)'
    }
  }

  // 处理子路由点击
  const handleChildRouteClick = (child: RouteRecordRaw) => {
    // 正常路由跳转逻辑
    const targetPath = `/dispatch/DataApplication/${currentParentRoute.value}/${String(child.name)}`
    router.push({
      path: targetPath,
      query: route.query, // 保持查询参数
    })
  }
</script>
