<template>
  <div class="dispatch-tree-wrapper">
    <TableTree
      ref="dispatchTree"
      :treeId="treeId"
      :contextmenuOption="contextmenuOption"
      @select="selectNodes"
      @dblclick="dblclickNode"
      @loaded="treeLoaded"
      :withPageHeader="true"
      :filter="true"
    />
  </div>
</template>

<script lang="ts" setup>
  import TableTree from '@/components/common/tableTree'
  import { useRouteParams } from '@/router'
  import { useI18n } from 'vue-i18n'
  import { computed, useTemplateRef } from 'vue'
  import bfutil, { DeviceTypes, getDeviceMapLonLat } from '@/utils/bfutil'
  import bfprocess from '@/utils/bfprocess'
  import bftree from '@/utils/bftree'
  import { mapFlyTo } from '@/utils/map'
  import { useDynamicGroup } from '@/utils/dynamicGroup/useDynamicGroup'

  // 支持查看设备状态的终端类型
  const SupportStatusDeviceTypes = [
    DeviceTypes.Device,
    DeviceTypes.Mobile,
    DeviceTypes.VirtualClusterDevice,
    DeviceTypes.PocDevice,
    DeviceTypes.VirtualRepeater,
  ]
  const { setRouteParams: _setRouteParams } = useRouteParams()

  const { t } = useI18n()

  const { treeId } = defineProps({
    treeId: {
      type: String,
      default: 'bfdx-tree-' + Date.now(),
    },
  })

  const treeIdComputed = computed(() => treeId)

  const {
    addDynamicGroupNode: _addDynamicGroupNode,
    updateDynamicGroupNode: _updateDynamicGroupNode,
    deleteDynamicGroupNode: _deleteDynamicGroupNode,
  } = useDynamicGroup(treeIdComputed)

  const dispatchTreeRef = useTemplateRef('dispatchTree')

  const contextmenuOption = computed(() => {
    const menu = [
      {
        title: t('tree.collapseAll'),
        cmd: 'collapseAll',
      },
      {
        title: t('tree.expandAll'),
        cmd: 'expandAll',
      },
      {
        title: t('tree.online'),
        cmd: 'displayOnline',
      },
      {
        title: t('tree.displayAllDev'),
        cmd: 'displayAll',
      },
    ]
    const cmdMenu = [
      {
        title: t('dialog.locateCtrl'),
        cmd: 'cb01',
      },
      {
        title: t('dialog.trailCtrl'),
        cmd: 'cb02',
      },
      {
        title: t('dialog.telecontrol'),
        cmd: 'cb09',
      },
      {
        title: t('tree.status'),
        cmd: 'stats',
      },
    ]
    const callMenu = [
      {
        title: t('tree.quickCall'),
        cmd: 'quickCall',
      },
    ]
    const tmpTreeId = `#${treeId}`
    const showCommonMenu = () => {
      // ui-contextmenu 没有ts类型定义
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      $(tmpTreeId).contextmenu('replaceMenu', [...callMenu, { title: '----' }, ...menu])
    }

    return {
      delegate: 'span.fancytree-node',
      autoFocus: true,
      menu: [],
      beforeOpen: (event, ui) => {
        const node = $.ui.fancytree.getNode(ui.target)
        if (!node) {
          return false
        }
        // Modify menu entries depending on node status
        // cmdMenu.forEach((item) => {
        //   $(treeId).contextmenu("enableEntry", item.cmd, !node.isFolder());
        // })
        // cmdMenu.forEach((item) => {
        //   $(treeId).contextmenu('showEntry', item.cmd, !node.isFolder())
        // })
        if (node.isFolder()) {
          showCommonMenu()
        } else {
          let hasStatus = false
          let key = node.key
          if (key.length > 36) {
            key = key.slice(0, 36)
          }
          const device = window.bfglob.gdevices.get(key)
          // 只有指定的设备才显示状态
          if (device && SupportStatusDeviceTypes.includes(device.deviceType)) {
            hasStatus = true
          }
          if (hasStatus) {
            // ui-contextmenu 没有ts类型定义
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            $(treeId).contextmenu('replaceMenu', [...callMenu, ...cmdMenu, { title: '----' }, ...menu])
          } else {
            showCommonMenu()
          }
        }
        node.setActive()
        ui.extraData.node = node
      },
      close: (_event, _ui) => {
        // var node = $.ui.fancytree.getNode(ui.target);
        // node.setFocus();
      },
      select: (event, ui) => {
        try {
          const node = $.ui.fancytree.getNode(ui.target) || ui.extraData.node
          const target = node ? node.key : ''
          let device

          if (!node) {
            return
          }

          switch (ui.cmd) {
            case 'quickCall':
              bfglob.emit('openMenuItem', 'command/bfSpeaking', vm => {
                vm.speakFast(node.data.dmrId)
              })
              break
            case 'cb01':
              if (bfutil.notSendCmdPermission()) {
                return
              }
              if (bfglob.userInfo.isSuper) {
                return
              }
              device = bfglob.gdevices.get(target)
              if (!device) {
                return
              }
              bfprocess.cb01(
                { device: [device.dmrId] },
                {
                  spaceTime: 5,
                  count: 1,
                  size: 20,
                }
              )
              break
            case 'cb02':
              if (bfglob.userInfo.isSuper) {
                return
              }
              // 跳转到发送命令页面并选择跟踪监控radio和终端
              openVsendcmdWindowRadio(target, 'cb02', ui.target[0].innerText)
              break
            case 'cb09':
              if (bfglob.userInfo.isSuper) {
                return
              }
              openVsendcmdWindowRadio(target, 'cb09', ui.target[0].innerText)
              break
            case 'stats':
              const key = target.slice(0, 36)
              if (node && !node.isFolder()) {
                click_orgTree_channel(key)
              }
              break
            case 'collapseAll':
              bftree.treeExpandAll(treeId, false)
              break
            case 'expandAll':
              bftree.treeExpandAll(treeId, true)
              break
            case 'displayOnline':
              dispatchTreeRef.value?.showOnlineDevices()
              break
            case 'displayAll':
              dispatchTreeRef.value?.showAllDevices()
              break
          }
        } catch (e) {
          bfglob.console.error('contextmenu:', e)
        }
      },
    }
  })

  function openVsendcmdWindowRadio(_target, _radio, _targetName) {
    // todo
  }

  function click_orgTree_channel(_key) {
    // todo
  }

  const treeLoaded = () => {
    dispatchTreeRef.value?.toDictTree('bftree', dict => {
      // 过滤非单位节点

      return dict
    })
  }

  const selectNodes = (event, data) => {
    const orgKeys = []
    const deviceKeys = []
    const nodes = data.tree.getSelectedNodes()
    for (let i = 0; i < nodes.length; i++) {
      const node = nodes[i]
      if (node.data?.isOrg) {
        orgKeys.push(node.key)
      } else {
        deviceKeys.push(node.key)
      }
    }
    show_org_marker_by_select_node(orgKeys)
    show_device_marker_by_select_node(deviceKeys)
  }

  function show_org_marker_by_select_node(_key) {
    // todo
  }

  function show_device_marker_by_select_node(_key) {
    // todo
  }

  const dblclickNode = (event, data) => {
    if (!data.node) {
      return
    }
    // data.node.setSelected(true)

    if (data.node.data.isOrg) {
      const org = bfglob.gorgData.get(data.node.key)
      if (!org) {
        return
      }
      // use relative org marker lonlat
      const lonlatInfo = bfglob.gorgData.getOrgMapMakerPointLonlatInfo(org.rid)
      if (!lonlatInfo.lonlat) {
        return
      }
      mapFlyTo(lonlatInfo.lonlat, lonlatInfo.showLevel)
    } else {
      const device = bfglob.gdevices.get(data.node.key)
      if (!device) {
        return
      }
      mapFlyTo(getDeviceMapLonLat(device))
    }
  }
</script>

<style lang="scss">
  .dispatch-tree-wrapper {
    height: 100%;
    width: 100%;
  }
</style>
