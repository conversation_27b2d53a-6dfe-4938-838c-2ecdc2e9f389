<template>
  <div class="function-list-container relative flex flex-col justify-center items-center gap-[20px] h-full py-[20px]">
    <template v-for="item in listItems" :key="item.name">
      <DispatchFunctionListItem :item="item" :isSelected="currentSelectedItemName === item.name" @click="handleItemClick(item)" />
    </template>
  </div>
</template>

<script setup lang="ts">
  import openDialog from '@/utils/dialog'
  import { computed, ref } from 'vue'
  import { useI18n } from 'vue-i18n'
  import { FunctionListItemType } from './DispatchFunctionListItem.vue'

  const listImgs = import.meta.glob('@/assets/images/dispatch/function_list/**/*', { eager: true })
  Object.keys(listImgs).forEach(key => {
    const k = key.replace(/\/src\/assets\/images\/dispatch\/function_list\//, '')
    listImgs[k] = listImgs[key]
    delete listImgs[key]
  })

  const toIconPath = (name: string) => {
    return {
      inactiveIconPath: listImgs[`${name}.svg`]?.default ?? listImgs[`${name}.svg`],
      activeIconPath: listImgs[`${name}_selected.svg`]?.default ?? listImgs[`${name}_selected.svg`],
    }
  }

  const { t } = useI18n()
  // TODO: set dialogComponent
  const listItems = computed<Array<FunctionListItemType>>(() => {
    return [
      {
        label: t('dispatch.functionList.sendCommand'),
        name: 'sendCommand',
        ...toIconPath('send_command'),
      },
      {
        label: t('dispatch.functionList.keyboardDial'),
        name: 'keyboardDial',
        ...toIconPath('keyboard_dial'),
      },
      {
        label: t('dispatch.functionList.audioSwitch'),
        name: 'audioSwitch',
        ...toIconPath('audio_switch'),
      },
      {
        label: t('dispatch.functionList.broadcastCall'),
        name: 'broadcastCall',
        ...toIconPath('broadcast_call'),
      },
    ]
  })

  const currentSelectedItemName = ref<string | null>(null)

  const handleItemClick = (item: FunctionListItemType) => {
    if (!item.dialogComponent) {
      return
    }
    currentSelectedItemName.value = item.name

    openDialog(item.dialogComponent, {
      onClose: () => {
        currentSelectedItemName.value = null
      },
    })
  }
</script>

<style scoped lang="scss">
  .function-list-container {
    position: relative;
    border-width: 1px;
    border-image: linear-gradient(to right bottom, rgba($color: #9ca6d6, $alpha: 0.88), rgba($color: #7a88cb, $alpha: 0.47)) 30;
    background: linear-gradient(to right bottom, rgba($color: #00000b, $alpha: 0.38), rgba($color: #00000b, $alpha: 0.26));
  }
  .function-list-container::after {
    content: '';
    position: absolute;
    top: -6px;
    right: 9px;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 4, 0.27);
    filter: blur(100px);
    z-index: -1;
  }
</style>
