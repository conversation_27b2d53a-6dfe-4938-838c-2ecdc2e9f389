<template>
  <data-form-editor
    ref="formEditor"
    class="page-controller"
    editor-class="controller-editor"
    table-class="controller-data-table"
    :title="$t('nav.ctrlData')"
    :tableName="dataTable.name"
    :data="dataTable.body"
    :column="dthead"
    :detailHead="detailHead"
    :detailBodyName="dataTable.detailBodyName"
    :detail-render="detailRender"
    :detailBodyIndex="false"
    :getNewData="getNewData"
    :parseDataForEdit="parseDataForEdit"
    :before-action="beforeAction"
    :getFormRef="() => $refs.controllerDataEditorForm"
    :show-close="true"
    top="10vh"
    @row-delete="onDelete"
    @row-update="onUpdate"
    @row-new="onNew"
    @row-dblclick="tableRowDblclick"
    @status-change="editStatusChange"
    @close="editorOnClose"
  >
    <template #form="{ formData, isNewStatus }">
      <el-form
        ref="controllerDataEditorForm"
        :model="formData"
        :label-width="labelWidth"
        :rules="getRules(formData, isNewStatus)"
        :validate-on-rule-change="false"
        label-position="left"
        class="grid grid-cols-1 data-editor-form"
      >
        <el-form-item prop="orgId">
          <template #label>
            <EllipsisText :content="$t('dialog.parentOrg') + ':'" class="text-base font-medium text-[#fff] h-[50px] leading-[50px]" />
          </template>
          <BfSelect
            v-model="formData.orgId"
            :placeholder="$t('dialog.select')"
            filterable
            clearable
            :no-match-text="$t('dialog.noMatchText')"
            :disabled="hasParentControllerTypes.includes(formData.controllerType)"
            class="h-[50px] w-full"
          >
            <el-option v-for="item in selOrgList" :key="item.rid" :label="item.label" :value="item.rid" />
          </BfSelect>
        </el-form-item>
        <el-form-item prop="selfId" class="form-item-ellipsis">
          <template #label>
            <EllipsisText :content="$t('dialog.deviceName') + ':'" class="text-base font-medium text-[#fff] h-[50px] leading-[50px]" />
          </template>
          <BfInput v-model="formData.selfId" :maxlength="16" class="h-[50px] w-full" />
        </el-form-item>
        <el-form-item prop="dmrId">
          <template #label>
            <EllipsisText content="DMRID:" class="text-base font-medium text-[#fff] h-[50px] leading-[50px]" />
          </template>
          <DmridInput v-model="formData.dmrId" class="h-[50px] w-full" />
        </el-form-item>
        <el-form-item v-show="formData.controllerType !== ControllerTypes.MeshGateway" prop="netTimeSlot" class="form-item-ellipsis">
          <template #label>
            <EllipsisText :content="$t('dialog.networkTimeSlot') + ':'" class="text-base font-medium text-[#fff] h-[50px] leading-[50px]" />
          </template>
          <BfInputNumberV2 v-model="formData.netTimeSlot" :min="0" :max="getMaxTimeSlotValue(formData.controllerType)" class="h-[50px] !w-full" />
        </el-form-item>
        <el-form-item class="repeater-type-form-item select-btn-box has-button" prop="controllerType">
          <template #label>
            <EllipsisText :content="$t('dialog.type') + ':'" class="text-base font-medium text-[#fff] h-[50px] leading-[50px]" />
          </template>
          <BfSelect v-model="formData.controllerType" filterable @change="val => controllerTypeChange(formData, val, isNewStatus)" class="h-[50px] w-full">
            <el-option v-for="item in controllerTypeList" :key="item.value" :label="item.label" :value="item.value" />
          </BfSelect>
          <el-button
            :disabled="!hasCustomSettingTypes.includes(formData.controllerType)"
            class="bf-form-item-button !h-full !ml-[10px] text-white"
            @click="openSettingDialog(formData.controllerType)"
          >
            {{ $t('header.setting') }}
          </el-button>
        </el-form-item>
        <el-form-item
          v-show="hasParentControllerTypes.includes(formData.controllerType)"
          prop="simulcastParent"
          class="form-item-ellipsis"
          :rules="hasParentControllerTypes.includes(formData.controllerType) ? requiredRule : undefined"
        >
          <template #label>
            <EllipsisText :content="$t('dialog.parentController') + ':'" class="text-base font-medium text-[#fff] h-[50px] leading-[50px]" />
          </template>
          <BfSelect v-model="formData.simulcastParent" filterable @change="val => simulcastParentChange(val, formData)" class="h-[50px] w-full">
            <template v-if="formData.controllerType === ControllerTypes.SimulcastRepeater">
              <el-option v-for="item in simulcastControllerList" :key="item.rid" :label="item.selfId" :value="item.rid" />
            </template>
            <template v-else>
              <el-option v-for="item in vcControllerList" :key="item.rid" :label="item.selfId" :value="item.rid" />
            </template>
          </BfSelect>
        </el-form-item>
        <el-form-item
          v-show="formData.controllerType === ControllerTypes.MeshGateway"
          key="devicePassword"
          class="form-item-ellipsis device-password"
          prop="devicePassword"
          :rules="formData.controllerType === ControllerTypes.MeshGateway ? requiredRule : undefined"
        >
          <template #label>
            <EllipsisText :content="$t('loginDlg.password') + ':'" class="text-base font-medium text-[#fff] h-[50px] leading-[50px]" />
          </template>
          <BfInput v-model="formData.devicePassword" type="password" show-password :maxlength="16" class="h-[50px] w-full" />
        </el-form-item>
        <el-form-item class="lngLat-form-item form-item-ellipsis">
          <template #label>
            <EllipsisText :content="$t('dialog.lngLat') + ':'" class="text-base font-medium text-[#fff] h-[50px] leading-[50px]" />
          </template>
          <lon-lat v-model:lon="formData.lon" v-model:lat="formData.lat" />
        </el-form-item>
        <el-form-item prop="location" class="form-item-ellipsis">
          <template #label>
            <EllipsisText :content="$t('dialog.installLocation') + ':'" class="text-base font-medium text-[#fff] h-[50px] leading-[50px]" />
          </template>
          <BfInput v-model="formData.location" :maxlength="128" class="h-[50px] w-full" />
        </el-form-item>
        <el-form-item prop="note">
          <template #label>
            <EllipsisText :content="$t('dialog.notes') + ':'" class="text-base font-medium text-[#fff] h-[50px] leading-[50px]" />
          </template>
          <BfInput v-model="formData.note" type="textarea" resize="none" :rows="3" :maxlength="128" class="w-full" />
        </el-form-item>

        <el-form-item v-if="isShowPingSetting" key="setting.ping" prop="setting.ping">
          <template #label>
            <EllipsisText content="Ping:" class="text-base font-medium text-[#fff] h-[50px] leading-[50px]" />
          </template>
          <BfInputNumberV2 v-model="formData.setting.ping" :min="2" :max="30" class="h-[50px] !w-full" />
        </el-form-item>

        <div class="flex flex-wrap justify-between items-center">
          <!-- 插话 -->
          <el-form-item prop="canTalk">
            <BfCheckbox v-model="formData.canTalk" :disabled="!canCallTypes.includes(formData.controllerType)" class="h-[50px] w-full">
              <span v-text="$t('dialog.canTalk')" />
            </BfCheckbox>
          </el-form-item>

          <!-- 本地通话视为联网通话配置 -->
          <el-form-item v-if="hasLocal2netControllerTypes.includes(formData.controllerType)" key="setting.local2net" prop="setting.local2net">
            <BfCheckbox v-model="formData.setting.local2net" class="h-[50px] w-full">
              {{ $t('dialog.local2net') }}
            </BfCheckbox>
          </el-form-item>

          <!-- 常规DMR接入配置 -->
          <el-form-item
            v-if="hasModTraditionalDmrPermission && AllowGeneralDmrCallInTypes.includes(formData.controllerType)"
            class="traditional-dmr-allow-net-call"
          >
            <BfCheckbox
              v-model="formData.traditionalDmrAllowNetCall"
              :true-value="1"
              :false-value="0"
              @change="value => traditionalDmrAllowNetCallOnchange(value)"
              class="h-[50px] w-full"
            >
              <span>{{ $t('dialog.allowGeneralDMROnlineCall') }}</span>
            </BfCheckbox>
            <el-button
              type="default"
              class="bf-form-item-button !ml-[10px]"
              :disabled="!formData.traditionalDmrAllowNetCall"
              @click="setGeneralDmrListenGroupEditorState(true)"
            >
              {{ $t('dialog.setReceivingGroup') }}
            </el-button>
          </el-form-item>
        </div>
      </el-form>
    </template>

    <template #default="{ formData, isNewStatus }">
      <!-- 设置电话网关参数 -->
      <controller-gateway
        v-if="showPhoneGateway"
        v-model="controlGatewayData"
        v-model:visible="showPhoneGateway"
        :isNewMode="isNewStatus"
        :refControllerId="formData.rid"
        :orgId="formData.orgId"
        :add-data-method="addControlGatewayFunc"
        :up-data-method="upControlGatewayFunc"
        :del-data-method="delControlGatewayFunc"
        :request-data-method="requestControlGatewayDataMethod"
      />

      <!-- 设置SIP网关参数 -->
      <sip-gateway-settings v-if="showSipGateway" v-model:visible="showSipGateway" v-model="formData.simInfo" />

      <prochat-gateway-setting v-if="showProchatGateway" v-model:visible="showProchatGateway" v-model="prochatGatewayModel" />

      <sip-server-gateway-setting v-if="showSipServerGateway" v-model:visible="showSipServerGateway" v-model="sipServerGatewayModel" />

      <!-- 设置静态收听组 -->
      <select-listen-group
        v-if="hasModTraditionalDmrPermission && showTraditionalDmrListenGroup"
        v-model:visible="showTraditionalDmrListenGroup"
        v-model="listenGroupKeys"
        tree-id="traditional-dmr-listen-group"
      />

      <!-- 中继状态对话框 -->
      <RepeaterStatusMonitor
        v-if="repeaterStatusLoaded"
        v-model="statusVisible"
        :repeaterStatus="repeaterStatus"
        :lastReceiveTime="repeaterStatusLastReceiveTime"
        @refresh="refreshRepeaterStatus"
      />
    </template>
  </data-form-editor>
</template>

<script>
  import { defineAsyncComponent } from 'vue'
  import { cloneDeep } from 'lodash'
  import { DefaultFormData as DefaultSipFormData } from '@/components/common/sipGatewaySettings/common'
  import { DefaultFormData as DefaultProchatFormData } from '@/components/common/prochatGatewaySetting.vue'
  import { DefaultFormData as DefaultSipServerFormData } from '@/components/common/sipServerGatewaySetting.vue'
  import bfproto, { kcpPackageName } from '@/modules/protocol'
  import dbCmd from '@/modules/protocol/db.pb.cmd'
  import bfProcess from '@/utils/bfprocess'
  import bfutil, { ControllerTypes, DefOrgRid, DeviceTypes, dmrid2ssmmm, formatDmrIdLabel, getDbSubject, ssmmm2dmrid } from '@/utils/bfutil'
  import maputil from '@/utils/map'
  import bfNotify, { messageBox, Types } from '@/utils/notify'
  import bfCrypto, { encodeMeshGatewayDevicePassword } from '@/utils/crypto'
  import bfTime, { nowUtcTime } from '@/utils/time'
  import vueMixin from '@/utils/vueMixin'
  import { v1 as uuid } from 'uuid'
  import { checkLicenseWithModuleName, getAuthModuleI18nKey, getLicense, LicenseModuleNames } from '@/utils/bfAuth'
  import BfInput from '@/components/bfInput/main'
  import BfInputNumberV2 from '@/components/bfInputNumber/main'
  import BfSelect from '@/components/bfSelect/main'
  import { convertPxToRem, calcScaleSize } from '@/utils/setRem'
  import DmridInput from '@/components/common/DmridInput.vue'
  import BfCheckbox from '@/components/bfCheckbox/main'
  import DataFormEditor, { EditStatus } from '@/components/common/DataFormEditor.vue'
  import EllipsisText from '@/components/common/EllipsisText.vue'
  import validateRules from '@/utils/validateRules'
  import bfUserSettings from '@/utils/userSettings'
  import { prochatControllerRid, prochatUserRid } from '@/utils/prochatDeviceInfoList'
  import { useRouteParams } from '@/router'

  const licenses = getLicense().lic?.licenses ?? {}
  // 是否有常规DMR接入权限
  const hasModTraditionalDmrPermission = checkLicenseWithModuleName(licenses, LicenseModuleNames.ModTraditionalDmr)
  // 是否有SVT接入授权
  const hasSvtAuth = checkLicenseWithModuleName(licenses, LicenseModuleNames.ModSvt)
  /** @type {Map<string, object[]>} */
  const StaticSubscribes = new Map()

  const sipServerControllerRid = '16161616-1616-1616-1616-161616161616'
  const defaultUuid = '00000000-0000-0000-0000-000000000000'
  const DefaultSetting = {
    ping: 10,
    // setting 增加bool属性local2net,true 保存下来，false删除掉此字段
    local2net: false,
  }
  const defaultData = {
    rid: '',
    orgId: bfutil.getBaseDataOrgId(),
    selfId: '',
    dmrId: '',
    note: '',
    netTimeSlot: 2,
    controllerType: 0,
    location: '',
    canTalk: false,
    simulcastParent: defaultUuid,
    traditionalDmrAllowNetCall: 0,
    baseStationRid: '',
    lon: '',
    lat: '',
    signalAera: '{}',
    room: '0',
    sipNo: '',
    simInfo: { ...DefaultSipFormData },
    setting: { ...DefaultSetting },
    // Mesh网关的密码
    devicePassword: '',
  }
  // 可以插话的中继类型 控制器类型 0:中继 1:同播中继
  const canCallTypes = [ControllerTypes.Repeater, ControllerTypes.SimulcastRepeater]
  // mesh网关的联网信道默认为8，且不可修改
  const MeshControllerDefaultNetTimeSlot = 8
  // 创建终端的控制器网关类型，不包含带插话的控制器类型
  const CreateTerminalTypes = [
    ControllerTypes.SipGateway, // SIP网关
    ControllerTypes.MeshGateway, // Mesh网关
    ControllerTypes.ProchatGateway, // Prochat网关
  ]
  // 控制器创建的终端的类型映射
  const TerminalRelationTypes = {
    [ControllerTypes.SipGateway]: DeviceTypes.SipGatewayDevice,
    [ControllerTypes.MeshGateway]: DeviceTypes.MeshGateway,
    [ControllerTypes.ProchatGateway]: DeviceTypes.ProchatGatewayDevice,
    default: DeviceTypes.VirtualRepeater, // 带插话功能控制器创建的终端类型：中继虚拟终端
  }
  const CanDeleteDeviceTypes = Object.keys(TerminalRelationTypes).map(key => TerminalRelationTypes[key])
  // 所有自动创建终端的控制器类型
  const AllCreateTerminalTypes = [
    ...canCallTypes, // 带插话
    ...CreateTerminalTypes, // SIP, Mesh等网关
  ]

  // 控制器创建的终端对应的优先级
  const TerminalTypesPriority = {
    [DeviceTypes.SipGatewayDevice]: 0,
    // [DeviceTypes.VirtualRepeater]: 2,
    default: 1,
  }

  // 允许传统DMR终端接入的类型
  const AllowGeneralDmrCallInTypes = [ControllerTypes.Repeater, ControllerTypes.SimulcastRepeater, ControllerTypes.SimulcastController]

  // 缓存中继订阅状态事件标记
  const RepeaterStatusEventCache = new Set()
  const DefaultRepeaterStatus = {
    antErr: 0,
    channelId: 0,
    deviceDmrid: 0,
    fanErr: 0,
    gpsErr: 0,
    ipAddr: 0,
    powerValue: 0,
    rxFrequency: 0,
    rxPllErr: 0,
    signal: 0,
    tmpErr: 0,
    tmpValue: 0,
    txFrequency: 0,
    txPllErr: 0,
    volErr: 0,
    volValue: 0,
  }

  // 存储formData为key validateDmrId为value的弱映射
  const validateDmrIdWeakMap = new WeakMap()
  const { setRouteParams, getRouteParams } = useRouteParams()

  export default {
    name: 'BfControllers',
    mixins: [vueMixin],
    data() {
      return {
        statusVisible: false,
        repeaterStatusLoaded: false,
        repeaterStatus: { ...DefaultRepeaterStatus },
        repeaterStatusLastReceiveTime: 0,
        dataTable: {
          name: 'controllersTable',
          body: bfutil.objToArray(bfglob.gcontrollers.getAll()),
          detailBodyName: 'controllerGateway',
        },
        selOrgList: bfglob.gorgData.getList(),
        editStatus: EditStatus.None,

        // 新建控制器时，设置的电话网关参数
        controlGatewayData: [],
        showPhoneGateway: false,
        showSipGateway: false,
        showProchatGateway: false,
        showSipServerGateway: false,
        // Ctrl+Alt+P/p
        isShowPingSetting: false,

        prochatGatewayModel: {
          ...DefaultProchatFormData,
        },
        sipServerGatewayModel: {
          ...DefaultSipServerFormData,
        },
        // 显示编辑传统dmr中继接收组标记
        showTraditionalDmrListenGroup: false,
        // 传统DMR终端接入，中继静态接收组列表
        listenGroupKeys: [],
      }
    },
    methods: {
      getRules(formData, _isNewStatus) {
        if (!validateDmrIdWeakMap.has(formData)) {
          const validateDmrId = validateRules.validateDmrId({
            encodeMsgType: 'db_controller',
            decodeMsgType: 'db_controller_list',
            command: dbCmd.DB_CONTROLLER_GETBY,
            dataManager: bfglob.gcontrollers,
          })
          validateDmrIdWeakMap.set(formData, validateDmrId)
        }
        return {
          orgId: [validateRules.required('blur')],
          selfId: [validateRules.required('blur')],
          lon: [
            {
              validator: function (rule, value, callback) {
                bfutil.mustNumberRule(rule, value, callback)
              },
              trigger: 'change',
            },
          ],
          lat: [
            {
              validator: function (rule, value, callback) {
                bfutil.mustNumberRule(rule, value, callback)
              },
              trigger: 'change',
            },
          ],
          dmrId: [
            {
              validator: (rule, value, callback) => {
                if (value !== formData.dmrId) {
                  return callback()
                }
                const controller = bfglob.gcontrollers.getDataByIndex(value)
                if (controller && controller.rid === formData.rid) {
                  return callback()
                }

                const validate = validateDmrIdWeakMap.get(formData)
                if (validate) {
                  validate(rule, value, callback)
                } else {
                  callback()
                }
              },
              trigger: 'change',
            },
          ],
        }
      },
      wrapSipConfig(row) {
        if (row.controllerType === ControllerTypes.SipGateway) {
          try {
            row.sipNo = row.simInfo.sipNo
            row.simInfo = JSON.stringify(row.simInfo)
          } catch (_e) {
            row.sipNo = ''
            row.simInfo = '{}'
          }
        } else {
          row.simInfo = '{}'
        }
      },
      async onDelete(row) {
        await this.delete_controller_data(row, dbCmd.DB_CONTROLLER_DELETE)

        // 尝试删除可能存在的虚拟终端
        if (AllCreateTerminalTypes.includes(row.controllerType)) {
          // 已经删除或不用删除则返回true
          this.deleteVirtualDevice(row.dmrId).catch(_err => {
            bfNotify.messageBox(this.$t('msgbox.unableDeleteRepeaterVirtualDevice'), 'warning')
          })
        }

        //是Prochat网关，则需要删除用户
        if (row.controllerType === ControllerTypes.ProchatGateway) {
          const prochatUser = this.getProchatUser()
          if (prochatUser) await this.deleteProchatUser(prochatUser)
        }

        // 是Mesh网关，则需要删除单位
        if (row.controllerType === ControllerTypes.MeshGateway) {
          const ssmmm = dmrid2ssmmm(row.dmrId)
          const orgDmrId = ssmmm2dmrid(ssmmm.ss, ssmmm.mmm, 1)
          const org = bfglob.gorgData.getDataByIndex(orgDmrId)
          if (org) await this.deleteVirtualOrg(org).catch(() => false)
        }
      },
      async onUpdate(row, done) {
        // 如果是SIP网关，则先检测是否有设置SIP网关参数，有则设置网关参数
        this.wrapSipConfig(row)

        const oldData = bfglob.gcontrollers.get(row.rid)

        // 禁止由Prochat网关类型更改为其他设备类型或由其他设备类型更改为Prochat网关类型
        if (oldData.controllerType !== row.controllerType) {
          if (oldData.controllerType === ControllerTypes.ProchatGateway || row.controllerType === ControllerTypes.ProchatGateway) {
            bfNotify.messageBox(this.$t('msgbox.canNotEditProchatGatewayType'), 'error').catch()
            return
          }
          if (oldData.controllerType === ControllerTypes.SipServerGateway || row.controllerType === ControllerTypes.SipServerGateway) {
            bfNotify.messageBox(this.$t('msgbox.canNotEditSipServerGatewayType'), 'error').catch()
            return
          }
        }

        this.wrapProchatConfig(row)

        //sipServer setting config
        if (row.controllerType === ControllerTypes.SipServerGateway) {
          row.setting = this.sipServerGatewayModel
        }

        if (this.svtControllerType.includes(row.controllerType) && !hasSvtAuth) {
          const i18nKey = getAuthModuleI18nKey(LicenseModuleNames.ModSvt)
          messageBox(this.$t('auth.noSpecifiedModuleAuth', { module: this.$t(i18nKey) }), Types.error)
          return
        }

        let canNext = true
        // 尝试更新Prochat网关终端对应的专属用户
        let prochatUser = null
        if (row.controllerType === ControllerTypes.ProchatGateway) {
          const oldProchatUser = this.getProchatUser()
          if (oldProchatUser) {
            prochatUser = await this.updateProchatUser(row).catch(() => false)
          } else {
            prochatUser = await this.addProchatUser(row).catch(() => false)
          }
          canNext = !!prochatUser
        }

        const addVirtualTerminal = async () => {
          const virtualData = this.createVirtualDeviceData(row)
          if (row.controllerType === ControllerTypes.ProchatGateway) {
            Object.assign(virtualData, {
              deviceUser: prochatUserRid || DefOrgRid,
            })
          }
          canNext = await this.addVirtualDevice(virtualData).catch(() => false)
        }
        const updateVirtualTerminal = async () => {
          // 如果dmrId变更，则使用旧的dmrId查找终端
          let dmrId = row.dmrId
          if (dmrId !== oldData.dmrId) {
            dmrId = oldData.dmrId
          }
          const device = bfglob.gdevices.getDataByIndex(dmrId)
          if (device) {
            const virtualData = {
              ...device,
              ...this.createVirtualDeviceData(row),
            }
            const res = await this.updateVirtualDevice(virtualData).catch(() => {
              bfNotify.messageBox(this.$t('msgbox.upgradeSipGatewayDeviceFailed'), 'error')
              return false
            })
            canNext = !!res
          }
        }
        const deleteVirtualTerminal = async () => {
          let dmrId = row.dmrId
          if (dmrId !== oldData.dmrId) {
            dmrId = oldData.dmrId
          }
          await this.deleteVirtualDevice(dmrId).catch(() => false)
        }

        // 类型无变更处理逻辑
        const controllerTypeNoChangeHandler = async () => {
          // 控制器带虚拟终端，不需要处理
          if (!AllCreateTerminalTypes.includes(row.controllerType)) {
            return
          }

          // 如果是Mesh网关，需要单位的数据
          if (row.controllerType === ControllerTypes.MeshGateway) {
            // 使用旧的控制器的dmrId查找对应的单位，更新单位的数据
            const ssmmm = dmrid2ssmmm(oldData.dmrId)
            const orgDmrId = ssmmm2dmrid(ssmmm.ss, ssmmm.mmm, 1)
            const org = bfglob.gorgData.getDataByIndex(orgDmrId)
            // 更新单位的数据
            if (org) await this.updateVirtualOrg(org, row)
          }

          // 有对应的虚拟终端，需要区分"可插话"中继和网关
          // CreateTerminalTypes中的类型，直接更新
          if (CreateTerminalTypes.includes(row.controllerType)) {
            await updateVirtualTerminal()
            return
          }

          // 可插话的中继，需要判断是否有选中"可插话"配置
          // "可插话"配置变更，则判断添加或删除
          if (row.canTalk !== oldData.canTalk) {
            // 选中，添加
            if (row.canTalk) {
              await addVirtualTerminal()
            } else {
              // 没有选中，删除
              await deleteVirtualTerminal()
            }
            return
          }

          if (row.canTalk) {
            // 没有变更则更新
            await updateVirtualTerminal()
          } else {
            // 尝试删除可能存在的虚拟终端
            await deleteVirtualTerminal()
          }
        }
        // 类型变更后处理逻辑
        const controllerTypeChangeHandler = async () => {
          if (oldData.controllerType === ControllerTypes.SipGateway) {
            row.sipNo = ''
          }
          // 当前已经不再是Mesh网关，则删除创建的单位
          if (row.controllerType !== ControllerTypes.MeshGateway) {
            // 使用旧的控制器的dmrId查找对应的单位
            const ssmmm = dmrid2ssmmm(oldData.dmrId)
            const orgDmrId = ssmmm2dmrid(ssmmm.ss, ssmmm.mmm, 1)
            const org = bfglob.gorgData.getDataByIndex(orgDmrId)
            if (org) await this.deleteVirtualOrg(org)
          }

          // 从带虚拟终端变更为不带虚拟终端的类型，则删除虚拟终端
          if (!AllCreateTerminalTypes.includes(row.controllerType)) {
            await deleteVirtualTerminal()
            return
          }

          // 变更为Mesh网关，添加对应的单位
          if (row.controllerType === ControllerTypes.MeshGateway) {
            this.createVirtualOrg(row)
          }

          // 旧类型没有虚拟终端，变更为有虚拟终端
          if (!AllCreateTerminalTypes.includes(oldData.controllerType)) {
            // 当前中继类型变更为"可插话"中继，则需要判断是否有选中"可插话"配置
            if (canCallTypes.includes(row.controllerType)) {
              // "可插话"配置变更，如果没有选中则删除，否则更新
              if (row.canTalk) {
                // 选中，添加
                await addVirtualTerminal()
              } else {
                // 没有选中，尝试删除可能存在的虚拟终端
                // await deleteVirtualTerminal()
              }
            } else {
              // 中继变更为CreateTerminalTypes中的类型，直接添加虚拟终端
              await addVirtualTerminal()
            }
            return
          }

          // 旧类型为有虚拟终端，变更为有虚拟终端，如：可插话中继->Mesh网关，或者Mesh网关->可插话中继
          // 变更为"可插话"中继，则需要判断是否有选中"可插话"配置
          if (canCallTypes.includes(row.controllerType)) {
            // "可插话"配置变更，如果没有选中则删除，否则更新
            if (row.canTalk) {
              // 选中，则更新即可
              await updateVirtualTerminal()
            } else {
              // 没有选中，删除虚拟终端
              await deleteVirtualTerminal()
            }
            return
          }

          // 从"可插话"中继变更为CreateTerminalTypes中的类型，需要判断为更新或添加
          // 旧的插话配置选中，则更新即可
          if (oldData.canTalk) {
            await updateVirtualTerminal()
            return
          }

          // 添加虚拟终端
          await addVirtualTerminal()
        }

        // 类型有变更，需要同步处理中继虚拟终端
        if (oldData.controllerType === row.controllerType) {
          await controllerTypeNoChangeHandler()
        } else {
          await controllerTypeChangeHandler()
        }

        if (!canNext) return

        const isOk = await this.update_controller_data(row, dbCmd.DB_CONTROLLER_UPDATE)
        if (!isOk) return
        done()
      },
      // addNewCb：存在这个回调函数则需要继续添加新的一行
      async onNew(row, done, addNewCb) {
        // sip网关，处理配置参数
        this.wrapSipConfig(row)

        if (row.controllerType === ControllerTypes.ProchatGateway) {
          const prochatGateway = this.getProchatGateway()
          if (prochatGateway) {
            bfNotify.messageBox(this.$t('msgbox.prochatGatewayExist'), 'warning')
            return
          }
          if (!this.checkArgsFieldsIsSeted(this.prochatGatewayModel, row.controllerType)) {
            bfNotify.messageBox(this.$t('msgbox.prochatArgsNotSet'), 'error').catch()
            return
          }
        }

        // Prochat网关，处理配置参数
        this.wrapProchatConfig(row)

        // SIP服务器网关，处理配置参数
        if (row.controllerType === ControllerTypes.SipServerGateway) {
          const sipServer = this.getSipServerGateway()
          if (sipServer) {
            bfNotify.messageBox(this.$t('msgbox.sipServerGatewayExist'), 'warning').catch()
            return
          }
          if (!this.checkArgsFieldsIsSeted(this.sipServerGatewayModel, ControllerTypes.SipServerGateway)) {
            bfNotify.messageBox(this.$t('msgbox.sipServerArgsNotSet'), 'error').catch()
            return
          }
          row.setting = this.sipServerGatewayModel
        }

        if (this.svtControllerType.includes(row.controllerType) && !hasSvtAuth) {
          const i18nKey = getAuthModuleI18nKey(LicenseModuleNames.ModSvt)
          messageBox(this.$t('auth.noSpecifiedModuleAuth', { module: this.$t(i18nKey) }), Types.error)
          return
        }

        // 控制器数据操作的前置操作失败后回滚操作事件回调
        const rollbackEvents = []
        const rollback = () => {
          rollbackEvents.forEach(event => event())
        }
        // 处理控制器需要添加相同DMRID的终端流程
        let canNext = true

        // 尝试添加Prochat网关终端对应的专属用户
        let prochatUser = null
        if (row.controllerType === ControllerTypes.ProchatGateway) {
          prochatUser = await this.addProchatUser(row).catch(() => false)
          canNext = !!prochatUser
          if (canNext) {
            rollbackEvents.push(() => this.deleteProchatUser(prochatUser))
          } else {
            return rollback()
          }
        }

        // 先判断是否带插话功能，或为指定的拥有对应终端的控制器，先添加一个终端
        if (row.canTalk || CreateTerminalTypes.includes(row.controllerType)) {
          const virtualData = this.createVirtualDeviceData(row)
          if (row.controllerType === ControllerTypes.ProchatGateway) {
            Object.assign(virtualData, {
              deviceUser: prochatUserRid || DefOrgRid,
            })
          }
          const res = await this.addVirtualDevice(virtualData).catch(() => false)
          canNext = !!res
          if (canNext) {
            rollbackEvents.push(() => this.deleteVirtualDevice(row.dmrId))
          } else {
            return rollback()
          }
        }

        // 尝试添加Mesh网关对应的单位数据
        if (row.controllerType === ControllerTypes.MeshGateway) {
          const virtualOrg = await this.createVirtualOrg(row).catch(_err => false)
          canNext = !!virtualOrg
          if (canNext) {
            rollbackEvents.push(() => this.deleteVirtualOrg(virtualOrg))
          } else {
            return rollback()
          }
        }

        if (!canNext) return rollback()

        // 最后才创建控制器数据
        const isOk = await this.add_controller_data(row, dbCmd.DB_CONTROLLER_INSERT)
        if (!isOk) return rollback()
        if (addNewCb) {
          const __data = this.getNewData()
          __data.orgId = row.orgId
          __data.selfId = bfutil.customNumberIncrement(row.selfId)
          __data.dmrId = bfutil.dmrIdAutoIncrement(row.dmrId)
          __data.controllerType = row.controllerType
          __data.netTimeSlot = row.netTimeSlot
          __data.simulcastParent = row.simulcastParent
          // 重置标签页数据
          bfutil.resetForm(this, 'controllerDataEditorForm')
          addNewCb(__data)
          return
        }
        done()
      },
      // 返回一个新的默认参数对象
      getNewData() {
        return cloneDeep(defaultData)
      },
      beforeAction(status, data) {
        if (bfutil.notEditDataPermission()) {
          return Promise.reject('No permission')
        }
        if (status === EditStatus.Add) {
          data.dmrId = data.dmrId === '' ? '00080000' : data.dmrId
          while (bfglob.gcontrollers.getDataByIndex(data.dmrId)) {
            data.dmrId = bfutil.dmrIdAutoIncrement(data.dmrId)
            if (data.dmrId === '000FFFFF') {
              break
            }
          }
        }

        return Promise.resolve(true)
      },
      parseDataForEdit(formData) {
        // 如果有开启了常规DMR接入，则查询固定订阅收听表
        if (hasModTraditionalDmrPermission && AllowGeneralDmrCallInTypes.includes(formData.controllerType) && formData.traditionalDmrAllowNetCall) {
          const dmrId = formData.dmrId
          this.queryControllerStaticSubscribes(dmrId).then(subs => {
            this.listenGroupKeys = subs.map(s => s.groupDmrId)
          })
        }

        try {
          formData.setting = {
            ...JSON.parse(formData.setting),
          }
          switch (formData.controllerType) {
            case ControllerTypes.ProchatGateway:
              this.prochatGatewayModel = {
                ...DefaultProchatFormData,
                ...formData.setting,
              }
              break
            case ControllerTypes.SipServerGateway:
              this.sipServerGatewayModel = {
                ...DefaultSipServerFormData,
                ...formData.setting,
              }
              break
            default:
              formData.setting = {
                ...DefaultSetting,
                ...formData.setting,
              }
          }
          formData.simInfo = {
            ...DefaultSipFormData,
            ...JSON.parse(formData.simInfo),
          }
        } catch (e) {
          bfglob.console.error('controller.vue parseDataForEdit catch:', e)
        }
      },
      editorOnClose() {
        this.resetListenGroupKeys()
        this.controlGatewayData = []
      },

      /**
       * 刷新中继当前状态
       * @param {string} dmrId 十六进制dmrId
       */
      refreshRepeaterStatus(dmrId) {
        this.requestRepeaterStatus(dmrId)
      },
      onOpenRepeaterStatusDialog(data) {
        this.repeaterStatusLoaded = true
        this.$nextTick(() => {
          this.statusVisible = true
        })
        this.repeaterStatus = data.repeaterStatus ?? {
          ...DefaultRepeaterStatus,
          deviceDmrid: parseInt(data.dmrId, 16),
        }
        this.repeaterStatusLastReceiveTime = data.repeaterStatusLastReceiveTime ?? 0
      },
      // 初始化中继状态监控展示按钮的代理事件
      initRepeaterShowStatusEvent() {
        setTimeout(() => {
          const tableBody = document.querySelector('.controller-data-table > tbody')
          if (!tableBody) {
            this.initRepeaterShowStatusEvent()
            return
          }

          tableBody.addEventListener('click', evt => {
            let target = evt.target
            // 只处理特定的dom元素
            if (!target.classList.toString().includes('show-repeater-status-btn')) {
              return
            }

            // 如果是图标，则找到父级的按钮元素
            if (target.classList.contains('show-repeater-status-btn--icon')) {
              target = target.parentNode
            }

            const data = bfglob.gcontrollers.get(target.dataset.rid)
            data && this.onOpenRepeaterStatusDialog(data)
          })
        }, 100)
      },
      /**
       * 判断是否支持状态监控，同播控制器、虚拟集群控制器不支持
       * @param {Record<string, any>} data 控制器数据
       * @returns {boolean}
       */
      isSupportMonitor(data) {
        const excludes = [ControllerTypes.SimulcastController, ControllerTypes.VCController]
        if (excludes.includes(data.controllerType)) return false
        return (data.functions ?? []).includes('8')
      },
      /**
       * 生成订阅中继状态的NATS事件名称
       * @param {string} dmrId 十六进制字符串
       * @returns {string} 事件名称
       */
      generateRepeaterStatusMonitorEventName(dmrId) {
        return `monitor${parseInt(dmrId, 16)}`
      },
      /**
       * 处理中继上线、下线的事件，订阅或取消订阅中继的运行状态
       * @param {Record<string, any>} controller 控制器数据
       */
      onUpdateControllerStats(controller) {
        // 判断是否支持监控
        if (!this.isSupportMonitor(controller)) return

        const dmrId = controller.dmrId

        // 下线
        if (controller.ctrlStats === 0) {
          const subject = this.generateRepeaterStatusMonitorEventName(dmrId)
          bfglob.server.unsubscribe(subject)
          RepeaterStatusEventCache.delete(dmrId)
          return
        }

        // 上线
        if (controller.ctrlStats === 1) {
          const subject = this.generateRepeaterStatusMonitorEventName(dmrId)
          // 避免可能重复订阅
          !RepeaterStatusEventCache.has(dmrId) && bfglob.server.subscribe(subject, this.processRepeaterStatus)
          // 上线后，主动查询一次状态
          this.requestRepeaterStatus(dmrId)
        }
      },
      /**
       * 同步中继监控状态参数
       * @param {import('@/modules/protocol/bf_proto.json').nested.bfkcp.nested.res_repeater_state} status 中继的监控状态，包含异常状态
       * @param controller 中继数据
       */
      syncRepeaterStatus(status, controller) {
        if (this.repeaterStatus.deviceDmrid !== status.deviceDmrid) {
          return
        }
        this.repeaterStatus = status
        this.repeaterStatusLastReceiveTime = controller.repeaterStatusLastReceiveTime
      },
      /**
       * todo 处理中继上报状态
       * @param {string} msg_data NATS的消息数据，base64
       */
      processRepeaterStatus(msg_data) {
        // 解析出状态数据对象
        const rpc_cmd_obj = bfproto.bfdx_natsMsg2rpcCmd(msg_data, kcpPackageName)
        // 判断是185查询的结果，还是188主动上报的结果
        if (rpc_cmd_obj.cmd === 185) {
          const repeaterState = bfproto.decodeMessage(rpc_cmd_obj.body, 'res_repeater_state', kcpPackageName)
          bfglob.console.log('processRepeaterStatus 185', repeaterState)
          // 更新状态监控对话框参数，缓存状态到中继数据上
          const dmrId = bfutil.toHexDmrId(repeaterState.deviceDmrid, false)
          const controller = bfglob.gcontrollers.getDataByIndex(dmrId)
          if (!controller) {
            return
          }
          controller.repeaterStatus = repeaterState
          controller.repeaterStatusLastReceiveTime = Date.now()
          // 同步当前的状态对象
          this.syncRepeaterStatus(repeaterState, controller)
        } else if (rpc_cmd_obj.cmd === 188) {
          const status = bfproto.decodeMessage(rpc_cmd_obj.body, 'repeater_err_status', kcpPackageName)
          bfglob.console.log('processRepeaterStatus 188', status)
          // 更新状态监控对话框参数
          const dmrId = bfutil.toHexDmrId(status.deviceDmrid, false)
          const controller = bfglob.gcontrollers.getDataByIndex(dmrId)
          if (!controller) {
            return
          }
          controller.repeaterStatus = Object.assign(controller.repeaterStatus ?? {}, status)
          controller.repeaterStatusLastReceiveTime = Date.now()
          // 同步当前的状态对象
          this.syncRepeaterStatus(controller.repeaterStatus, controller)
        }
      },
      /**
       * 请求指定中继的状态
       * @param {string} dmrId 十六进制
       */
      requestRepeaterStatus(dmrId) {
        const options = {
          rpcCmdFields: {
            optInt: parseInt(dmrId, 16),
          },
        }
        // 查询结果在中订阅中处理
        return bfproto.sendMessage(3185, null, null, `db.${bfglob.sysId}`, options).catch(err => {
          bfglob.console.warn('请求中继当前状态异常：', dmrId, err)
        })
      },
      // 初始化订阅中继状态，中继上线、下线都要添加订阅和取消订阅
      initSubscribeRepeaterStatus() {
        for (let i = 0; i < this.dataTable.body.length; i++) {
          const data = this.dataTable.body[i]
          // 中继在线，且要支持监控功能
          if (data.ctrlStats !== 1 || !this.isSupportMonitor(data)) {
            continue
          }

          const subject = this.generateRepeaterStatusMonitorEventName(data.dmrId)
          // 订阅中继运行状态数据变更
          bfglob.server.subscribe(subject, this.processRepeaterStatus)
          RepeaterStatusEventCache.add(data.dmrId)

          // 查询该中继当前的状态
          this.requestRepeaterStatus(data.dmrId).catch(err => bfglob.console.error('requestRepeaterStatus catch:', err))
        }
      },
      // 取消订阅中继状态变更
      unsubscribeRepeaterStatus() {
        for (let i = 0; i < this.dataTable.body.length; i++) {
          const data = this.dataTable.body[i]
          // 只有支持监控功能的才需要取消订阅
          if (!this.isSupportMonitor(data)) {
            continue
          }

          const subject = this.generateRepeaterStatusMonitorEventName(data.dmrId)
          bfglob.server.unsubscribe(subject)
          RepeaterStatusEventCache.delete(data.dmrId)
        }
      },
      documentKeydownEvent(event) {
        // Ctrl+Alt+P/p
        if (event.code === 'KeyP' && event.altKey && event.ctrlKey) {
          if (this.editStatus !== 2) {
            return
          }
          this.isShowPingSetting = !this.isShowPingSetting
        }
      },
      simulcastRepeaterAction(ctx) {
        const { rid, parentRid } = ctx.dataset
        const data = bfglob.gcontrollers.get(rid)
        // const parent = bfglob.gcontrollers.get(parentRid)
        if (data?.ctrlStats !== 1) return

        // 打开中继写频界面
        setRouteParams('repeaterWriteFrequency', {
          fromController: true,
          rid,
          parentRid,
        })
        this.$router.push({
          name: 'repeaterWriteFrequency',
        })
      },
      detailRender(row, defaultRender) {
        // 同播控制器，加载显示管辖的同播中继
        const cusTypes = [ControllerTypes.SimulcastController, ControllerTypes.VCController]
        if (cusTypes.includes(row.controllerType)) {
          // 找到下辖的同播中继
          const repeaters = this.dataTable.body.filter(item => item.simulcastParent === row.rid)
          if (!repeaters.length) {
            return ''
          }

          let html = `<table cellpadding='0' cellspacing='0' border='0'
          class='display table table-striped row-details simulcast'
           width='100%'><thead><tr>`
          // 表头
          const header = [
            {
              label: '#',
              class: 'index',
            },
            {
              label: this.$t('dialog.action'),
              class: 'action',
            },
            {
              label: this.$t('dialog.repeaterName'),
              class: 'name',
            },
          ]
          html += header
            .map(col => {
              return `<th class="${col.class}">${col.label}</th>`
            })
            .join('')
          html += '</tr></thead><tbody>'
          html += repeaters
            .map((item, index) => {
              const action = `<button type='button'
                  class='simulcast-repeater-action el-button el-button--mini'
                  data-rid='${item.rid}' data-parent-rid='${row.rid}'>
                  <i class='mdi mdi-send-circle-outline'></i></button>`

              return `<tr>
                <td class="index">${index + 1}</td>
                <td class="action">${action}</td>
                <td class="name">${item.selfId}</td>
              </tr>`
            })
            .join('')

          html += '</tbody></table>'

          // 代理同播控制器子表按钮点击事件
          this.$nextTick(() => {
            const that = this
            $(this.$refs.formEditor.$refs.dataTable.$el)
              .find('.row-details.simulcast tbody')
              .off('click', 'button.simulcast-repeater-action')
              .on('click', 'button.simulcast-repeater-action', function () {
                that.simulcastRepeaterAction(this)
              })
          })

          return html
        }

        switch (row.controllerType) {
          case ControllerTypes.SipGateway:
            return this.createSipGatewayDetail(row.sipNo, row.simInfo)
          case ControllerTypes.ProchatGateway:
            return this.createProchatGatewayDetail(row.sipNo, row.setting)
          case ControllerTypes.SipServerGateway:
            return this.createSipServerGatewayDetial(row.setting)
          default:
            return defaultRender()
        }
      },
      createSipGatewayDetail(sipNo, simInfoStr) {
        let simInfo
        try {
          simInfo = JSON.parse(simInfoStr)
        } catch (_e) {
          simInfo = { ...DefaultSipFormData }
        }

        return `<div class="sip-info">
            <div class="sip-info-item">
              <span class="label">${this.$t('dialog.sipNo')}:</span>
              <span class="value">${sipNo}</span>
            </div>
            <div class="sip-info-item">
              <span class="label">${this.$t('dialog.sipProxyGateway')}:</span>
              <span class="value">${simInfo.runBy8100 ? this.$t('dialog.yes') : this.$t('dialog.no')}</span>
            </div>
            <div class="sip-info-item ${simInfo.host ? '' : 'hidden'}">
              <span class="label">${this.$t('dialog.sipDomain')}:</span>
              <span class="value">${simInfo.host}</span>
            </div>
            <div class="sip-info-item ${simInfo.host ? '' : 'hidden'}">
              <span class="label">${this.$t('dialog.sipPort')}:</span>
              <span class="value">${simInfo.port}</span>
            </div>
          </div>`
      },
      createProchatGatewayDetail(sipNo, settingStr) {
        const prochatNo = sipNo
        let setting
        try {
          setting = JSON.parse(settingStr)
        } catch (_e) {
          setting = { ...DefaultProchatFormData }
        }

        return `<div class="sip-info">
              <div class="sip-info-item">
                <span class="label">${this.$t('dialog.prochatNo')}:</span>
                <span class="value">${prochatNo}</span>
              </div>
              <div class="sip-info-item ${setting.host ? '' : 'hidden'}">
                <span class="label">${this.$t('dialog.prochatDomain')}:</span>
                <span class="value">${setting.host}</span>
              </div>
              <div class="sip-info-item ${setting.host ? '' : 'hidden'}">
                <span class="label">${this.$t('dialog.prochatPort')}:</span>
                <span class="value">${setting.port}</span>
              </div>
              <div class="sip-info-item ${setting.userNo ? '' : 'hidden'}">
                <span class="label">${this.$t('dialog.prochatUserNo')}:</span>
                <span class="value">${setting.userNo}</span>
              </div>
           </div>`
      },
      createSipServerGatewayDetial(settingStr) {
        let setting
        try {
          setting = JSON.parse(settingStr)
        } catch (_e) {
          setting = { ...DefaultSipServerFormData }
        }

        return `<div class="sip-info">
              <div class="sip-info-item">
                <span class="label">${this.$t('dialog.sipServerGatewayDomain')}:</span>
                <span class="value">${setting.host}</span>
              </div>
              <div class="sip-info-item ${setting.port ? '' : 'hidden'}">
                <span class="label">${this.$t('dialog.sipServerGatewayListenPort')}:</span>
                <span class="value">${setting.port}</span>
              </div>
              <div class="sip-info-item ${setting.rtpStart ? '' : 'hidden'}">
                <span class="label">${this.$t('dialog.sipServerGatewayRTPPortRange')}:</span>
                <span class="value">${setting.rtpStart}~${setting.rtpEnd}</span>
              </div>
          </div>`
      },
      //  选择上级同播控制器时，同步上级单位
      simulcastParentChange(val, data) {
        // 上级控制器数据
        const parentController = bfglob.gcontrollers.get(val)
        if (!parentController) {
          return
        }
        if (parentController.orgId !== data.orgId) {
          data.orgId = parentController.orgId
        }
      },
      setPhoneGatewayState(state = true) {
        this.showPhoneGateway = state
      },
      getMaxTimeSlotValue(type) {
        if (type === ControllerTypes.VCController) {
          return 32
        }
        return 2
      },
      controllerTypeChange(controller, val) {
        controller.simulcastParent = ''
        const maxTimeSlot = this.getMaxTimeSlotValue(val)
        controller.netTimeSlot = Math.min(maxTimeSlot, controller.netTimeSlot)
        if (!this.hasLocal2netControllerTypes.includes(val)) {
          controller.setting.local2net = false
        }
        controller.devicePassword = ''

        // 不支持插话的类型
        if (!canCallTypes.includes(val)) {
          controller.canTalk = false
        }
      },
      openSettingDialog(controllerType) {
        switch (controllerType) {
          case ControllerTypes.SipGateway:
            this.setSipGatewayEditorState(true)
            break
          case ControllerTypes.Gateway:
            this.setPhoneGatewayState(true)
            break
          case ControllerTypes.ProchatGateway:
            this.setProchatGatewayEditorState(true)
            break
          case ControllerTypes.SipServerGateway:
            this.setSipServerGatewayEditorState(true)
        }
      },
      setSipGatewayEditorState(state = true) {
        this.showSipGateway = state
      },
      getProchatGateway() {
        return bfglob.gcontrollers.get(prochatControllerRid)
      },
      setProchatGatewayEditorState(state = true) {
        if (this.editStatus === 1) {
          if (this.getProchatGateway()) {
            this.showProchatGateway = false
            bfNotify.messageBox(this.$t('msgbox.prochatGatewayExist'), 'warning')
            return
          }
        }
        this.showProchatGateway = state
      },
      resetListenGroupKeys() {
        this.listenGroupKeys = []
      },
      traditionalDmrAllowNetCallOnchange(value) {
        if (!value) {
          this.resetListenGroupKeys()
        }
      },
      setGeneralDmrListenGroupEditorState(status) {
        this.showTraditionalDmrListenGroup = status
      },
      editStatusChange(status) {
        this.editStatus = status
      },
      tableRowDblclick(data) {
        bfNotify.messageBox(this.$t('dialog.controllerPointJumpTip'))
        maputil.dbclickJumpToMarker(data, 4)
      },
      deleteLocalGatewayData(refControllerId) {
        if (!refControllerId) {
          return
        }
        const cgList = bfglob.gcontrollerGateway.getDataListByRefControllerId(refControllerId)
        cgList.forEach(item => {
          bfglob.emit('delete_global_db_controller_gateway', item)
        })
      },
      checkValidDmrIdAndSelfId(virtualData) {
        if (!virtualData) {
          return false
        }
        if (bfglob.gdevices.getDataByIndex(virtualData.dmrId)) {
          bfNotify.messageBox(this.$t('msgbox.repeaterVirtualDeviceRepeatDmrId'), 'warning')
          return false
        }
        const devices = bfglob.gdevices.getAll()
        const list = Object.keys(devices).filter(key => {
          const data = devices[key]
          return data.selfId === virtualData.selfId
        })

        const res = list.length === 0
        if (!res) {
          bfNotify.messageBox(this.$t('msgbox.repeaterVirtualDeviceRepeatSelfId'), 'warning')
        }

        return res
      },
      /**
       * 通过控制器创建需要添加的终端数据
       * @param controller 控制器数据
       * @returns terminal 返回创建的终端数据
       */
      createVirtualDeviceData(controller) {
        return {
          selfId: controller.selfId,
          orgId: controller.orgId,
          dmrId: controller.dmrId,
          deviceType: TerminalRelationTypes[controller.controllerType] ?? TerminalRelationTypes.default,
        }
      },
      addVirtualDevice(data) {
        return new Promise((resolve, reject) => {
          // 先本地检测dmrId和selfId是否可用
          const checked = this.checkValidDmrIdAndSelfId(data)
          if (!checked) {
            return reject(false)
          }

          const msgObj = {
            ...data,
            rid: uuid(),
            orgId: data.orgId || bfutil.DefOrgRid,
            deviceUser: data.deviceUser || bfutil.DefOrgRid,
            virOrgs: '',
            note: '',
            setting: '{}',
            deviceType: data.deviceType || 4,
            channel: JSON.stringify({ channels: [] }),
            channelLastModifyTime: bfTime.nowUtcTime(),
            priority: TerminalTypesPriority[data.deviceType] ?? TerminalTypesPriority.default,
            gatewayFilterRid: bfutil.DefOrgRid,
            lastRfConfigTime: bfTime.nowUtcTime(),
            lastRfWriteTime: bfTime.nowUtcTime(),
            devGroup: '',
            pocSettingLastModifyTime: bfTime.nowUtcTime(),
          }

          bfproto
            .sendMessage(dbCmd.DB_DEVICE_INSERT, msgObj, 'db_device', getDbSubject())
            .then(rpc_cmd_obj => {
              const res = {
                code: 0,
                info: rpc_cmd_obj.resInfo,
              }
              if (rpc_cmd_obj.resInfo === '+OK') {
                bfglob.emit('add_global_deviceData', cloneDeep(msgObj))
                res.code = 100
                resolve(res)
              } else {
                const msgOpts = {
                  isVnode: true,
                  detailMessage: rpc_cmd_obj.resInfo,
                }
                // 需要判断dmrId和selfId是否重复
                if (rpc_cmd_obj.resInfo.includes('db_device_self_id_key')) {
                  bfNotify.messageBox(this.$t('msgbox.repeaterVirtualDeviceRepeatSelfId'), Types.warning, msgOpts)
                  res.code = 1
                } else if (rpc_cmd_obj.resInfo.includes('db_device_dmr_id_key')) {
                  res.code = 2
                  bfNotify.messageBox(this.$t('msgbox.repeaterVirtualDeviceRepeatDmrId'), Types.warning, msgOpts)
                }

                reject(res)
                bfglob.console.warn('addVirtualDevice failed:', rpc_cmd_obj, msgObj)
              }
            })
            .catch(err => {
              bfglob.console.warn('addVirtualDevice error:', err)
              reject(false)
            })
        })
      },
      /**
       * 创建Mesh网关对应的单位
       * @param {Record<string, any>} controller 控制器数据
       * @returns {Promise<Record<string, any> | null>} 返回创建的单位数据
       */
      createVirtualOrg(controller) {
        return new Promise((resolve, reject) => {
          // 转换控制器dmrId
          const ssmmm = dmrid2ssmmm(controller.dmrId)
          const orgDmrId = ssmmm2dmrid(ssmmm.ss, ssmmm.mmm, 1)
          const msgObj = {
            rid: uuid(),
            parentOrgId: controller.orgId || bfutil.DefOrgRid,
            orgSortValue: 200,
            orgSelfId: controller.selfId,
            orgShortName: controller.selfId,
            orgFullName: controller.selfId,
            note: '',
            orgIsVirtual: 2,
            dmrId: orgDmrId,
            orgImg: '11111111-1111-1111-1111-111111111111',
            setting: JSON.stringify({
              controlDmrId: controller.dmrId,
            }),
            creator: bfglob.userInfo.rid,
          }

          bfproto
            .sendMessage(dbCmd.DB_ORG_INSERT, msgObj, 'db_org', getDbSubject())
            .then(rpc_cmd_obj => {
              bfglob.console.log('createVirtualOrg res:', rpc_cmd_obj)
              if (rpc_cmd_obj.resInfo === '+OK') {
                bfglob.emit('add_global_orgData', msgObj)

                // 为当前账号添加该组织权限
                const privelege_data = {
                  rid: uuid(),
                  userRid: bfglob.userInfo.rid,
                  userOrg: msgObj.rid,
                  includeChildren: 1,
                }
                bfProcess.add_user_privelege(privelege_data, dbCmd.DB_USER_PRIVELEGE_INSERT)

                resolve(msgObj)
              } else {
                resolve(null)
                const msgOpts = {
                  isVnode: true,
                  detailMessage: rpc_cmd_obj.resInfo,
                }
                if (rpc_cmd_obj.resInfo.includes('db_org_org_self_id_key')) {
                  bfNotify.messageBox(this.$t('msgbox.repeatNo') + ` (${msgObj.orgSelfId})`, Types.warning, msgOpts)
                  return
                }
                if (rpc_cmd_obj.resInfo.includes('db_org_dmr_id_key')) {
                  bfNotify.messageBox(this.$t('msgbox.repeatDMRID') + ` (${msgObj.dmrId})`, Types.warning, msgOpts)
                  return
                }
                if (rpc_cmd_obj.resInfo.includes('db_org_org_short_name_key')) {
                  bfNotify.messageBox(this.$t('msgbox.repeatOrgShortName') + ` (${msgObj.orgShortName})`, Types.warning, msgOpts)
                  return
                }
                bfNotify.messageBox(this.$t('msgbox.addError'), Types.error, msgOpts)
              }
            })
            .catch(err => {
              bfglob.console.warn('createVirtualOrg catch:', err)
              reject(err)
            })
        })
      },
      /**
       * 处理控制器`setting`字段
       * @param {Object} 控制器数据对象
       * @returns {String} 返回传递的`setting`参数json字符串
       */
      wrapperSetting(data) {
        const setting = cloneDeep(data.setting || {})
        if (data.controllerType === ControllerTypes.ProchatGateway) {
          return JSON.stringify(setting)
        }

        // ping default 10, not set
        if (setting.ping === 10) {
          delete setting.ping
        }
        // setting 增加bool属性local2net,true 保存下来，false删除掉此字段
        if (!this.hasLocal2netControllerTypes.includes(data.controllerType) || setting.local2net !== true) {
          delete setting.local2net
        }

        // Mesh网关类型，加密密码
        if (data.controllerType === ControllerTypes.MeshGateway) {
          setting.pw = encodeMeshGatewayDevicePassword(data.dmrId, data.devicePassword)
        } else {
          delete setting.pw
        }

        return JSON.stringify(setting)
      },
      /**
       * 处理控制器添加、更新时公共参数
       * @param {Record<string, any>} data 控制器数据
       * @param {'add'|'update'|'delete'} actionType 数据的操作类型，添加、更新、删除等
       */
      wrapperControllerData(data, _actionType) {
        // Mesh网关类型，额外处理联网信道
        data.netTimeSlot = data.controllerType === ControllerTypes.MeshGateway ? MeshControllerDefaultNetTimeSlot : data.netTimeSlot
        // 只有sip网关类型时，才使用当前的sip号码，否则默认使用dmrId
        data.sipNo = data.controllerType === ControllerTypes.SipGateway || data.controllerType === ControllerTypes.ProchatGateway ? data.sipNo : data.dmrId
      },
      /**
       * 添加设备固定订阅/静态收听表
       * @param {object} controller 添加的控制器数据
       * @param {string} dmrId 收听组的dmrId
       * @returns {Promise<db_static_subscribes>} 返回添加成功的收听表数据
       */
      addControllerStaticSubscribesOneItem(controller, dmrId) {
        const data = {
          rid: uuid(),
          controllerDmrId: controller.dmrId,
          groupDmrId: dmrId,
          creator: bfglob.userInfo.rid,
          createTime: nowUtcTime(),
        }
        return bfproto
          .sendMessage(dbCmd.DB_STATIC_SUBSCRIBES_INSERT, data, 'db_static_subscribes', getDbSubject())
          .then(rpc_cmd_obj => {
            if (rpc_cmd_obj.resInfo === '+OK') {
              return Promise.resolve(data)
            } else {
              return Promise.resolve(undefined)
            }
          })
          .catch(err => {
            bfglob.console.error('addControllerStaticSubscribesOneItem error:', err)
            return Promise.resolve(undefined)
          })
      },
      /**
       * 循环添加控制器固定订阅表数据
       * @param controller 控制器数据
       * @param listenGroup 需要订阅的收听组的dmrId列表
       */
      addControllerStaticSubscribes(controller, listenGroup) {
        // 创建数据库操作Promise对象
        const subList = listenGroup.map(dmrId => this.addControllerStaticSubscribesOneItem(controller, dmrId))
        // 返回所有操作结果
        return Promise.allSettled(subList).then(results => {
          const subs = StaticSubscribes.get(controller.dmrId) ?? []
          for (const state of results) {
            if (state.status === 'fulfilled' && state.value) {
              const subType = bfproto.bfdx_proto_msg_T('db_static_subscribes')
              subs.push(subType.create(state.value))
            }
          }
          StaticSubscribes.set(controller.dmrId, subs)
          return results
        })
      },
      add_controller_data(data, add_cmd) {
        const mapCenter = bfglob.map.getCenter().toArray()
        const msgObj = {
          ...data,
          rid: uuid(),
          orgId: data.orgId || bfutil.DefOrgRid,
          lon: data.lon || mapCenter[0],
          lat: data.lat || mapCenter[1],
          room: data.room || '0',
          sipNo: data.sipNo || data.dmrId,
          signalAera: data.signalAera || '{}',
          setting: this.wrapperSetting(data) || '{}',
          simInfo: data.simInfo || '{}',
          simulcastParent: data.simulcastParent || defaultUuid,
        }
        this.wrapperControllerData(msgObj, 'add')

        if (msgObj.controllerType === 14) {
          msgObj.rid = prochatControllerRid
        }

        if (msgObj.controllerType === 16) {
          msgObj.rid = sipServerControllerRid
        }

        return bfproto
          .sendMessage(add_cmd, msgObj, 'db_controller', getDbSubject())
          .then(rpc_cmd_obj => {
            bfglob.console.log('add controller res:', rpc_cmd_obj)
            const isOk = rpc_cmd_obj.resInfo === '+OK'
            if (isOk) {
              bfNotify.messageBox(this.$t('msgbox.addSuccess'), 'success')
              bfglob.emit('add_global_controllerData', msgObj)

              defaultData.orgId = msgObj.orgId
              defaultData.dmrId = bfutil.dmrIdAutoIncrement(msgObj.dmrId)
              defaultData.controllerType = msgObj.controllerType
              defaultData.netTimeSlot = msgObj.netTimeSlot
              defaultData.simulcastParent = msgObj.simulcastParent

              // 更新关联的网关设备数据
              if (msgObj.controllerType === ControllerTypes.Gateway) {
                this.addControlGatewayDataLogic(msgObj)
              }

              // 判断是否要配置设备固定收听组
              // 必须要有常规DMR终端接入权限
              if (hasModTraditionalDmrPermission && AllowGeneralDmrCallInTypes.includes(msgObj.controllerType) && msgObj.traditionalDmrAllowNetCall) {
                this.addControllerStaticSubscribes(msgObj, this.listenGroupKeys)
              }

              // 添加查询日志
              const note = this.$t('dialog.add') + msgObj.selfId + ' / ' + msgObj.dmrId + this.$t('msgbox.ctrlData')
              bfglob.emit('addnote', note)
            } else {
              // 设备添加失败，尝试删除已经创建的虚拟终端
              if (AllCreateTerminalTypes.includes(msgObj.controllerType)) {
                this.deleteVirtualDevice(msgObj.dmrId)
                // 删除创建的单位
                const ssmmm = dmrid2ssmmm(msgObj.dmrId)
                const orgDmrId = ssmmm2dmrid(ssmmm.ss, ssmmm.mmm, 1)
                const org = bfglob.gorgData.getDataByIndex(orgDmrId)
                if (org) this.deleteVirtualOrg(org)
              }

              if (rpc_cmd_obj.resInfo.includes('db_controller_dmr_id_key')) {
                bfNotify.warningBox(this.$t('msgbox.repeatDMRID'))
              } else if (rpc_cmd_obj.resInfo.includes('db_controller_sip_no_key')) {
                // sipNo重复
                bfNotify.warningBox(this.$t('msgbox.repeatSipNo'))
              } else if (rpc_cmd_obj.resInfo.includes('licence is full')) {
                bfNotify.warningBox(this.$t('msgbox.licenceIsFull'))
              } else {
                bfNotify.messageBox(this.$t('msgbox.addError'), 'error')
              }
            }
            return Promise.resolve(isOk)
          })
          .catch(err => {
            // 设备添加失败，尝试删除已经创建的虚拟终端
            if (this.hasParentControllerTypes.includes(msgObj.controllerType)) {
              this.deleteVirtualDevice(msgObj.dmrId)
            }

            bfglob.console.warn('add controller timeout:', err)
            bfNotify.messageBox(this.$t('msgbox.addError'), 'error')
            return Promise.resolve(false)
          })
      },
      updateVirtualDevice(data) {
        return new Promise((resolve, reject) => {
          const msgObj = {
            ...data,
            channelLastModifyTime: bfTime.nowUtcTime(),
            gatewayFilterRid: data.gatewayFilterRid || bfutil.DefOrgRid,
            deviceUser: data.deviceUser || bfutil.DefOrgRid,
            pocSettingLastModifyTime: bfTime.nowUtcTime(),
          }

          bfproto
            .sendMessage(dbCmd.DB_DEVICE_UPDATE, msgObj, 'db_device', getDbSubject())
            .then(rpc_cmd_obj => {
              if (rpc_cmd_obj.resInfo === '+OK') {
                bfglob.emit('update_global_deviceData', cloneDeep(msgObj))
                resolve(true)
              } else {
                reject(false)
                bfglob.console.warn('updateVirtualDevice failed:', rpc_cmd_obj, msgObj)
              }
            })
            .catch(err => {
              bfglob.console.warn('updateVirtualDevice error:', err)
              reject(false)
            })
        })
      },
      /**
       * 更新Mesh网关对应的单位
       * @param {Record<string, any>} org
       * @param {Record<string, any>} controller
       * @returns {Promise<Record<string, any>>} 返回当前更新的单位数据
       */
      updateVirtualOrg(org, controller) {
        return new Promise((resolve, reject) => {
          // 转换控制器dmrId
          const ssmmm = dmrid2ssmmm(controller.dmrId)
          const orgDmrId = ssmmm2dmrid(ssmmm.ss, ssmmm.mmm, 1)
          let settings = {}
          try {
            settings = JSON.parse(org.setting)
          } catch (_err) {
            // no-empty
          }

          const msgObj = {
            ...org,
            parentOrgId: controller.orgId || bfutil.DefOrgRid,
            orgSelfId: controller.selfId,
            orgShortName: controller.selfId,
            orgFullName: controller.selfId,
            dmrId: orgDmrId,
            setting: JSON.stringify({
              ...settings,
              controlDmrId: controller.dmrId,
            }),
          }

          bfproto
            .sendMessage(dbCmd.DB_ORG_UPDATE, msgObj, 'db_org', getDbSubject())
            .then(rpc_cmd_obj => {
              bfglob.console.log('updateVirtualOrg res:', rpc_cmd_obj)
              if (rpc_cmd_obj.resInfo === '+OK') {
                bfglob.emit('update_global_orgData', msgObj)
                resolve(msgObj)
              } else {
                resolve(null)
              }
            })
            .catch(err => {
              bfglob.console.warn('updateVirtualOrg catch:', err)
              reject(err)
            })
        })
      },
      /**
       * 删除控制器固定订阅的收听表的指定行数据
       * @param {object} controller 控制器数据
       * @param {db_static_subscribes} data 控制器订阅的收听组数据
       * @returns {Promise<boolean>} true为删除成功
       */
      deleteControllerStaticSubscribesOneItem(controller, data) {
        return bfproto
          .sendMessage(dbCmd.DB_STATIC_SUBSCRIBES_DELETE, data, 'db_static_subscribes', getDbSubject())
          .then(rpc_cmd_obj => {
            const isOk = rpc_cmd_obj.resInfo === '+OK'
            return Promise.resolve(isOk)
          })
          .catch(err => {
            bfglob.console.error('deleteControllerStaticSubscribesOneItem error:', err)
            return Promise.resolve(false)
          })
      },
      /**
       * 删除控制器多个订阅的表数据
       * @param {object} controller 控制器数据
       * @param {db_static_subscribes[]} dataList 控制器订阅的收听组数据列表
       */
      deleteControllerStaticSubscribes(controller, dataList) {
        // 创建数据库操作Promise对象
        const subList = dataList.map(data => this.deleteControllerStaticSubscribesOneItem(controller, data))
        // 返回所有操作结果
        return Promise.allSettled(subList).then(results => {
          const subs = StaticSubscribes.get(controller.dmrId) ?? []
          // 删除数组元素，从后面开始遍历，避免索引错误
          for (let i = results.length - 1; i >= 0; i--) {
            const state = results[i]
            if (state.status === 'fulfilled' && state.value) {
              subs.splice(i, 1)
            }
          }
          StaticSubscribes.set(controller.dmrId, subs)
          return results
        })
      },
      /**
       * 查询控制器的固定订阅表数据
       * @param {string} dmrId 控制器dmrId
       */
      queryControllerStaticSubscribes(dmrId) {
        return bfProcess.getStaticSubscribesByDmrId(dmrId).then(subs => {
          subs.length > 0 && StaticSubscribes.set(dmrId, subs)
          return subs
        })
      },
      /**
       * 更新设备固定订阅收听表
       * @param {object} controller 待更新的控制器
       * @param {string[]} listenGroup 当前选中的收听组列表，可能包含已经收听的数据
       */
      async updateControllerStaticSubscribes(controller, listenGroup) {
        const controllerDmrId = controller.dmrId
        const subs = StaticSubscribes.get(controllerDmrId) ?? []

        // 一个收听组都没有选择，则删除已经订阅的收听组
        if (!listenGroup.length) {
          await this.deleteControllerStaticSubscribes(controller, subs)
          return
        }

        // 控制器没有配置过订阅表，则直接添加
        if (!subs.length) {
          await this.addControllerStaticSubscribes(controller, listenGroup)
          return
        }

        // 筛选出还没有添加的订阅表数据
        const toAddList = listenGroup.filter(dmrId => !subs.some(s => s.groupDmrId === dmrId))
        if (toAddList.length) {
          await this.addControllerStaticSubscribes(controller, toAddList)
        }

        // 筛选出不再订阅的表数据
        const toDeleteList = subs.filter(s => !listenGroup.includes(s.groupDmrId))
        if (toDeleteList.length) {
          await this.deleteControllerStaticSubscribes(controller, toDeleteList)
        }
      },
      update_controller_data(data, up_db_cmd) {
        const mapCenter = bfglob.map.getCenter().toArray()
        const msgObj = {
          ...data,
          orgId: data.orgId || defaultUuid,
          baseStationRid: '',
          lon: data.lon || mapCenter[0],
          lat: data.lat || mapCenter[1],
          room: data.room || '0',
          sipNo: data.sipNo || data.dmrId,
          signalAera: data.signalAera || '{}',
          setting: this.wrapperSetting(data) || '{}',
          simInfo: data.simInfo || '{}',
          oldControllerType: data.oldControllerType,
          simulcastParent: data.simulcastParent || defaultUuid,
        }
        const oldData = bfglob.gcontrollers.get(data.rid)
        this.wrapperControllerData(msgObj, 'update')

        return bfproto
          .sendMessage(up_db_cmd, msgObj, 'db_controller', getDbSubject())
          .then(async rpc_cmd_obj => {
            bfglob.console.log('update controller res:', rpc_cmd_obj)
            const isOk = rpc_cmd_obj.resInfo === '+OK'
            if (isOk) {
              bfNotify.messageBox(this.$t('msgbox.upSuccess'), 'success')
              if (hasModTraditionalDmrPermission && AllowGeneralDmrCallInTypes.includes(msgObj.controllerType)) {
                // 直接删除所有的订阅表数据
                if (!msgObj.traditionalDmrAllowNetCall) {
                  // 给空数据，会删除旧的表数据
                  await this.updateControllerStaticSubscribes(msgObj, [])
                } else {
                  await this.updateControllerStaticSubscribes(msgObj, this.listenGroupKeys)
                }
              }
              // 更新全局组织机构数据
              bfglob.emit('update_global_controllerData', msgObj)

              // 如果控制器类型从电话网关更新为中继，则删除本地的控制器网关管理数据
              if (oldData.controllerType === ControllerTypes.Gateway && msgObj.controllerType !== ControllerTypes.Gateway) {
                this.deleteLocalGatewayData(msgObj.rid)
              }

              // 添加查询日志
              const note = this.$t('dialog.update') + msgObj.selfId + ' / ' + msgObj.dmrId + this.$t('msgbox.ctrlData')
              bfglob.emit('addnote', note)
            } else {
              if (rpc_cmd_obj.resInfo.includes('db_controller_dmr_id_key')) {
                bfNotify.warningBox(this.$t('msgbox.repeatDMRID'))
              } else if (rpc_cmd_obj.resInfo.includes('db_controller_sip_no_key')) {
                bfNotify.warningBox(this.$t('msgbox.repeatSipNo'))
              } else {
                bfNotify.messageBox(this.$t('msgbox.upError'), 'error')
              }
            }
            return Promise.resolve(isOk)
          })
          .catch(err => {
            bfglob.console.warn('update controller timeout:', err)
            bfNotify.messageBox(this.$t('msgbox.upError'), 'error')
            return Promise.resolve(false)
          })
      },
      deleteVirtualDevice(controlDmrId) {
        return new Promise((resolve, reject) => {
          // 中继虚拟对讲机dmrId与中继一致
          const device = bfglob.gdevices.getDataByIndex(controlDmrId)
          if (!device) {
            return resolve(true)
          }

          // 如果手台不是控制器创建的几个类型，则不能删除
          if (!CanDeleteDeviceTypes.includes(device.deviceType)) {
            return resolve(true)
          }

          const msgObj = {
            ...device,
          }

          bfproto
            .sendMessage(dbCmd.DB_DEVICE_DELETE, msgObj, 'db_device', getDbSubject())
            .then(rpc_cmd_obj => {
              if (rpc_cmd_obj.resInfo === '+OK') {
                bfglob.emit('delete_global_deviceData', cloneDeep(msgObj))
                resolve(true)
              } else {
                bfglob.console.warn('deleteVirtualDevice:', rpc_cmd_obj, msgObj)
                reject(false)
              }
            })
            .catch(err => {
              bfglob.console.warn('deleteVirtualDevice error:', err)
              reject(false)
            })
        })
      },
      /**
       * 删除创建的虚拟单位
       * @param {Record<string, any>} msgObj
       * @returns {Promise<boolean>}
       */
      deleteVirtualOrg(msgObj) {
        return new Promise((resolve, reject) => {
          bfproto
            .sendMessage(dbCmd.DB_ORG_DELETE, msgObj, 'db_org', getDbSubject())
            .then(rpc_cmd_obj => {
              if (rpc_cmd_obj.resInfo === '+OK') {
                bfglob.emit('delete_global_orgData', cloneDeep(msgObj))
                resolve(true)
              } else {
                bfglob.console.warn('deleteVirtualOrg:', rpc_cmd_obj, msgObj)
                reject(false)
              }
            })
            .catch(err => {
              bfglob.console.warn('deleteVirtualOrg error:', err)
              reject(err)
            })
        })
      },
      delete_controller_data(data, del_cmd) {
        return bfproto
          .sendMessage(del_cmd, data, 'db_controller', getDbSubject())
          .then(rpc_cmd_obj => {
            bfglob.console.log('delete controller res:', rpc_cmd_obj)
            const isOk = rpc_cmd_obj.resInfo === '+OK'
            if (isOk) {
              bfNotify.messageBox(this.$t('msgbox.delSuccess'), 'success')
              bfglob.emit('delete_global_controllerData', data)

              if (data.controllerType === ControllerTypes.Gateway) {
                this.deleteLocalGatewayData(data.rid)
              }
              StaticSubscribes.delete(data.dmrId)

              // 添加查询日志
              const note = this.$t('dialog.delete') + data.selfId + ' / ' + data.dmrId + this.$t('msgbox.ctrlData')
              bfglob.emit('addnote', note)
            } else {
              bfNotify.messageBox(this.$t('msgbox.delError'), 'error')
            }
            return Promise.resolve(isOk)
          })
          .catch(err => {
            bfglob.console.warn('delete controller timeout:', err)
            bfNotify.messageBox(this.$t('msgbox.delError'), 'error')
            return Promise.resolve(false)
          })
      },

      // 通过中继rid请求其下所有电话网关配置
      requestControlGatewayDataMethod(rid) {
        const msgObj = {
          refControllerId: rid,
        }
        const msgOpts = {
          rpcCmdFields: {
            origReqId: 'ref_controller_id',
            resInfo: '*',
          },
          decodeMsgType: 'db_controller_gateway_manage_list',
        }

        return bfproto
          .sendMessage(dbCmd.DB_CONTROLLER_GATEWAY_MANAGE_GETBY, msgObj, 'db_controller_gateway_manage', getDbSubject(), msgOpts)
          .then(rpc_cmd_obj => {
            const rows = rpc_cmd_obj.body.rows
            if (rpc_cmd_obj.resInfo === '+OK') {
              const data_arr = []
              for (let i = 0; i < rows.length; i++) {
                const item = rows[i]
                const data = bfProcess.setGlobControllerGatewayData(item)
                data_arr.push(data)
              }
              return Promise.resolve(data_arr)
            } else {
              bfglob.console.error('get db_controller_gateway_manage err:', rpc_cmd_obj.resInfo)
              return Promise.reject(rpc_cmd_obj.resInfo)
            }
          })
          .catch(err => {
            bfglob.console.warn('get db_controller_gateway_manage err:', err)
            return Promise.reject(err)
          })
      },
      // controllerGateway数据库表操作方法
      addControlGatewayFunc(data) {
        data.rid = data.rid || uuid()

        return bfproto
          .sendMessage(dbCmd.DB_CONTROLLER_GATEWAY_MANAGE_INSERT, data, 'db_controller_gateway_manage', getDbSubject())
          .then(rpc_cmd_obj => {
            if (rpc_cmd_obj.resInfo === '+OK') {
              bfglob.emit('add_global_db_controller_gateway', data)
              return Promise.resolve(data)
            } else {
              if (rpc_cmd_obj.resInfo.includes('db_controller_gateway_manage_ref_dev_id_key')) {
                bfNotify.warningBox(this.$t('msgbox.controllerGatewayUniqueDevice'))
              } else if (rpc_cmd_obj.resInfo.includes('db_controller_gateway_manage_phone_pos')) {
                bfNotify.warningBox(this.$t('msgbox.controllerGatewayUniquePhonePos'))
              }

              return Promise.reject(rpc_cmd_obj.resInfo)
            }
          })
          .catch(err => {
            bfglob.console.warn('add db_controller_gateway_manage timeout:', err)
            return Promise.reject(err)
          })
      },
      upControlGatewayFunc(data) {
        return bfproto
          .sendMessage(dbCmd.DB_CONTROLLER_GATEWAY_MANAGE_UPDATE, data, 'db_controller_gateway_manage', getDbSubject())
          .then(rpc_cmd_obj => {
            bfglob.console.log('update db_controller_gateway_manage res:', rpc_cmd_obj)
            if (rpc_cmd_obj.resInfo === '+OK') {
              bfNotify.messageBox(this.$t('msgbox.upSuccess'), 'success')
              bfglob.emit('update_global_db_controller_gateway', data)
              return Promise.resolve(data)
            } else {
              if (rpc_cmd_obj.resInfo.includes('db_controller_gateway_manage_ref_dev_id_key')) {
                bfNotify.warningBox(this.$t('msgbox.controllerGatewayUniqueDevice'))
              } else if (rpc_cmd_obj.resInfo.includes('db_controller_gateway_manage_phone_pos')) {
                bfNotify.warningBox(this.$t('msgbox.controllerGatewayUniquePhonePos'))
              }

              return Promise.reject(rpc_cmd_obj.resInfo)
            }
          })
          .catch(err => {
            bfglob.console.warn('update db_controller_gateway_manage timeout:', err)
            return Promise.reject(err)
          })
      },
      delControlGatewayFunc(data) {
        return bfproto
          .sendMessage(dbCmd.DB_CONTROLLER_GATEWAY_MANAGE_DELETE, data, 'db_controller_gateway_manage', getDbSubject())
          .then(rpc_cmd_obj => {
            if (rpc_cmd_obj.resInfo === '+OK') {
              bfNotify.messageBox(this.$t('msgbox.delSuccess'), 'success')
              bfglob.emit('delete_global_db_controller_gateway', data)
              return Promise.resolve(data)
            } else {
              bfNotify.messageBox(this.$t('msgbox.delError') + ' ' + rpc_cmd_obj.resInfo, 'error')
              return Promise.resolve(rpc_cmd_obj.resInfo)
            }
          })
          .catch(err => {
            bfglob.console.warn('delete db_controller_gateway_manage timeout:', err)
            return Promise.resolve(err)
          })
      },
      // 添加页/更新页操作controllerGateway数据逻辑
      addControlGatewayDataLogic(controller) {
        const setControllerData = data => {
          data.orgId = controller.orgId
          data.refControllerId = controller.rid
        }
        for (let i = 0; i < this.controlGatewayData.length; i++) {
          const data = this.controlGatewayData[i]
          setControllerData(data)
          this.addControlGatewayFunc(data).catch()
        }
      },
      // 同步dataTable数据
      upsetDataTableBody() {
        this.dataTable.body = bfutil.objToArray(bfglob.gcontrollers.getAll())
      },
      onAddNewController(params) {
        this.$nextTick(() => {
          this.$refs.formEditor?.addNewData()
          this.$refs.formEditor.formData.dmrId = params.dmrId
        })
      },
      addProchatUser(controller) {
        return new Promise((resolve, reject) => {
          if (!bfglob.userInfo.isSuper && bfutil.notEditDataPermission()) {
            return reject(false)
          }
          const setting = { ...bfUserSettings }
          const image = {
            rid: '22222222-2222-2222-2222-222222222222',
            fileName: 'default_user.png',
            fileContent: bfutil.default_user(),
            hash: bfCrypto.sha256(bfutil.default_user()),
          }
          const msgObj = {
            rid: prochatUserRid,
            orgId: controller.orgId,
            selfId: 'prochat',
            userName: controller.selfId,
            userTitle: '',
            userPhone: '',
            userLoginName: '',
            userLoginPass: '',
            userRfid: '',
            userImage: image.rid,
            note: '',
            userSetting: JSON.stringify(setting),
            image: image,
          }

          bfproto
            .sendMessage(dbCmd.DB_USER_INSERT, msgObj, 'db_user', getDbSubject())
            .then(rpc_cmd_obj => {
              if (rpc_cmd_obj.resInfo === '+OK') {
                bfglob.emit('add_global_userData', Object.assign({}, msgObj))
                resolve(msgObj)
              } else {
                resolve(false)
                if (rpc_cmd_obj.resInfo.includes('db_user_self_id_key')) {
                  bfNotify.warningBox(this.$t('msgbox.repeatNo'))
                  return
                }
                bfNotify.messageBox(this.$t('msgbox.addError'), 'error')
              }
            })
            .catch(err => {
              bfglob.console.warn('add prochat user timeout:', err)
              reject(err)
            })
        })
      },
      getProchatUser() {
        return bfglob.guserData.get(prochatUserRid)
      },
      updateProchatUser(data) {
        return new Promise((resolve, reject) => {
          if (!bfglob.userInfo.isSuper && bfutil.notEditDataPermission()) {
            return reject(false)
          }
          const oldUser = this.getProchatUser()
          const msgObj = {
            ...oldUser,
            orgId: data.orgId,
            userName: data.selfId,
            privelegeList: [],
            updatePrivelege: false,
          }

          bfproto
            .sendMessage(dbCmd.DB_USER_UPDATE, msgObj, 'db_user', getDbSubject())
            .then(rpc_cmd_obj => {
              if (rpc_cmd_obj.resInfo === '+OK') {
                bfglob.emit('update_global_userData', Object.assign({}, msgObj))
                resolve(msgObj)
              } else {
                resolve(false)
                if (rpc_cmd_obj.resInfo.includes('db_user_self_id_key')) {
                  bfNotify.warningBox(this.$t('msgbox.repeatNo'))
                  return
                }
                bfNotify.messageBox(this.$t('msgbox.upError'), 'error')
              }
            })
            .catch(err => {
              bfglob.console.warn('update prochat user timeout:', err)
              reject(err)
            })
        })
      },
      deleteProchatUser(msgObj) {
        return new Promise((resolve, reject) => {
          bfproto
            .sendMessage(dbCmd.DB_USER_DELETE, msgObj, 'db_user', getDbSubject())
            .then(rpc_cmd_obj => {
              if (rpc_cmd_obj.resInfo === '+OK') {
                bfglob.emit('delete_global_userData', msgObj)
                resolve(true)
              } else {
                bfglob.console.warn('deleteProchatUser:', rpc_cmd_obj, msgObj)
                reject(false)
              }
            })
            .catch(err => {
              bfglob.console.warn('deleteProchatUser error:', err)
              reject(err)
            })
        })
      },
      wrapProchatConfig(row) {
        if (row.controllerType === ControllerTypes.ProchatGateway) {
          row.sipNo = this.prochatGatewayModel.prochatNo
          row.setting = this.prochatGatewayModel
        }
      },
      /**
       * @param {object} config
       * @param {number} type - controllerType
       * 检测[type]网关是否有设置网关参数，如果指定的参数没有设置，则返回false
       */
      checkArgsFieldsIsSeted(config, type) {
        let checkFields
        switch (type) {
          case ControllerTypes.SipGateway:
            checkFields = ['host', 'port', 'sipNo', 'password']
            break
          case ControllerTypes.ProchatGateway:
            checkFields = ['host', 'port', 'prochatNo', 'userNo', 'password']
            break
          case ControllerTypes.SipServerGateway:
            checkFields = ['host', 'port', 'rtpStart', 'rtpEnd']
            break
        }

        if (checkFields.some(key => !config[key])) {
          return false
        }
        return true
      },
      getSipServerGateway() {
        return bfglob.gcontrollers.get(sipServerControllerRid)
      },
      setSipServerGatewayEditorState(state = true) {
        if (this.editStatus === EditStatus.Add) {
          if (this.getSipServerGateway()) {
            this.showSipServerGateway = false
            bfNotify.messageBox(this.$t('msgbox.sipServerGatewayExist'), 'warning')
            return
          }
        }
        this.showSipServerGateway = state
      },
    },
    mounted() {
      bfglob.on('add_global_controllerData', this.upsetDataTableBody)
      bfglob.on('update_global_controllerData', this.upsetDataTableBody)
      bfglob.on('delete_global_controllerData', this.upsetDataTableBody)
      bfglob.on('add_new_controller', this.onAddNewController)
      this.initRepeaterShowStatusEvent()
    },
    components: {
      DataFormEditor,
      controllerGateway: defineAsyncComponent(() => import('@/components/common/controllerGateway.vue')),
      sipGatewaySettings: defineAsyncComponent(() => import('@/components/common/sipGatewaySettings')),
      prochatGatewaySetting: defineAsyncComponent(() => import('@/components/common/prochatGatewaySetting')),
      sipServerGatewaySetting: defineAsyncComponent(() => import('@/components/common/sipServerGatewaySetting.vue')),
      DmridInput,
      selectListenGroup: defineAsyncComponent(() => import('@/components/common/selectListenGroup.vue')),
      RepeaterStatusMonitor: defineAsyncComponent(() => import('@/components/common/RepeaterStatusMonitor.vue')),
      LonLat: defineAsyncComponent(() => import('@/components/common/lonLat.vue')),
      BfInput,
      BfInputNumberV2,
      BfSelect,
      BfCheckbox,
      EllipsisText,
    },
    computed: {
      AllowGeneralDmrCallInTypes() {
        return AllowGeneralDmrCallInTypes
      },
      hasModTraditionalDmrPermission() {
        return hasModTraditionalDmrPermission
      },
      dthead() {
        return [
          {
            title: this.$t('dialog.status'),
            data: null,
            render: (_data, _type, row, _meta) => {
              // 中继在线状态
              const classes = {
                0: 'ctrlStats_disConnect',
                1: 'ctrlStats_connect',
                3: 'ctrlStats_alarm',
              }
              const tpl = `<span class="ctrlStats ${classes[row.ctrlStats] ?? ''}"><span class="hide">${row.ctrlStats}</span></span>`

              // 如果中继在线，且支持状态监控，则显示一个控件按钮
              if (row.ctrlStats > 0 && this.isSupportMonitor(row)) {
                return `<div class="repeater-monitor-ctrl">${tpl}<button type="button" class="el-button el-button--primary el-button--mini is-circle show-repeater-status-btn" data-rid="${row.rid}"><i class="mdi mdi-share-variant show-repeater-status-btn--icon"></i></button></div>`
              }

              return `<div class="repeater-monitor-ctrl">${tpl}</div>`
            },
            width: '50px',
          },
          {
            title: this.$t('dialog.parentOrg'),
            data: 'orgShortName',
            width: '100px',
          },
          {
            title: this.$t('dialog.deviceName'),
            data: 'selfId',
            width: this.isFR ? '150px' : this.isEN ? '120px' : '100px',
          },
          {
            title: this.$t('dialog.ctrlDMRID'),
            data: 'dmrId',
            width: this.isFR ? '165px' : '135px',
            render: data => {
              return formatDmrIdLabel(data)
            },
          },
          {
            title: this.$t('dialog.networkTimeSlot'),
            data: 'netTimeSlot',
            width: this.isFR ? '140px' : this.isEN ? '120px' : '80px',
            render: (_data, _type, row, _meta) => {
              return row.controllerType === ControllerTypes.MeshGateway ? this.$t('dialog.nothing') : `${row.netTimeSlot} ${this.$t('dialog.ge')}`
            },
          },
          {
            title: this.$t('dialog.type'),
            data: 'controllerType',
            width: this.isFR || this.isEN ? '140px' : '80px',
            render: data => {
              return this.controllerTypeList.find(item => item.value === data)?.label ?? ''
            },
          },
          {
            title: this.$t('dialog.canTalk'),
            data: null,
            width: this.isFR ? '120px' : '80px',
            render: (_data, _type, row, _meta) => {
              return row.canTalk ? this.$t('dialog.yes') : ''
            },
          },
          {
            title: this.$t('dialog.installLocation'),
            data: 'location',
            width: '180px',
          },
          {
            title: this.$t('dialog.notes'),
            data: 'note',
            width: '180px',
          },
        ]
      },
      detailHead() {
        return [
          {
            title: this.$t('dialog.interfacePos'),
            data: 'phonePos',
            width: '160px',
          },
          {
            title: this.$t('dialog.telephoneGateway'),
            data: 'refDevName',
            width: '200px',
          },
          {
            title: this.$t('dialog.telephoneNo'),
            data: 'phoneNo',
            width: '200px',
          },
        ]
      },
      // 同频同播、虚拟集群等中继类型
      hasParentControllerTypes() {
        return [ControllerTypes.SimulcastRepeater, ControllerTypes.VCRepeater]
      },
      hasLocal2netControllerTypes() {
        return [ControllerTypes.Repeater, ControllerTypes.SimulcastController, ControllerTypes.VCController]
      },
      hasCustomSettingTypes() {
        return [ControllerTypes.Gateway, ControllerTypes.SipGateway, ControllerTypes.ProchatGateway, ControllerTypes.SipServerGateway]
      },
      requiredRule() {
        return [validateRules.required()]
      },
      labelWidth() {
        return convertPxToRem(calcScaleSize(120)) + 'rem'
      },
      controllerTypeList() {
        // 控制器类型 0:中继 2:电话网关 1:同播中继 3：同播控制器 4: 虚拟集群控制器 5: 虚拟集群中继
        // 10:sip网关 12:mesh网关
        return [
          {
            label: this.$t('dialog.repeater'),
            value: ControllerTypes.Repeater,
          },
          {
            label: this.$t('dialog.controlTelephoneGateway'),
            value: ControllerTypes.Gateway,
          },
          {
            label: this.$t('dialog.simulcastRepeater'),
            value: ControllerTypes.SimulcastRepeater,
          },
          {
            label: this.$t('dialog.simulcastController'),
            value: ControllerTypes.SimulcastController,
          },
          {
            label: this.$t('dialog.vcController'),
            value: ControllerTypes.VCController,
          },
          {
            label: this.$t('dialog.vcRepeater'),
            value: ControllerTypes.VCRepeater,
          },
          {
            label: this.$t('dialog.sipGateway'),
            value: ControllerTypes.SipGateway,
          },
          {
            label: this.$t('dialog.meshGateway'),
            value: ControllerTypes.MeshGateway,
          },
          {
            label: this.$t('dialog.prochatGateway'),
            value: ControllerTypes.ProchatGateway,
          },
          {
            label: this.$t('dialog.sipServerGateway'),
            value: ControllerTypes.SipServerGateway,
          },
        ]
      },
      ControllerTypes() {
        return ControllerTypes
      },
      simulcastControllerList() {
        return this.dataTable.body.filter(item => item.controllerType === ControllerTypes.SimulcastController)
      },
      vcControllerList() {
        return this.dataTable.body.filter(item => item.controllerType === ControllerTypes.VCController)
      },
      canCallTypes() {
        return canCallTypes
      },
      // svt设备
      svtControllerType() {
        return [ControllerTypes.VCController, ControllerTypes.VCRepeater]
      },
    },
    beforeMount() {
      this.dataTable.body
        .filter(item => {
          return item.controllerType === 2
        })
        .map(item => {
          this.requestControlGatewayDataMethod(item.rid)
        })

      this.initSubscribeRepeaterStatus()

      // 订阅中继上、下线，重新处理中继状态的订阅信息
      bfglob.on('update_controller_stats', this.onUpdateControllerStats)
    },
    beforeUnmount() {
      this.unsubscribeRepeaterStatus()
      bfglob.off('update_controller_stats', this.onUpdateControllerStats)
    },
    activated() {
      // 订阅快捷键，显示控制器ping间隔
      document.addEventListener('keydown', this.documentKeydownEvent)
      this.$route.params = getRouteParams(this.$route.name)
      if (this.$route.params.dmrId) {
        this.onAddNewController(this.$route.params)
      }
    },
    deactivated() {
      document.removeEventListener('keydown', this.documentKeydownEvent)
    },
  }
</script>

<style lang="scss">
  .el-dialog.controller-editor {
    .el-form.data-editor-form {
      width: 500px;

      .el-form-item {
        .bf-input {
          .el-input__wrapper {
            height: 50px;
          }

          .el-input__inner {
            height: 50px;
            line-height: 50px;
          }
        }
      }

      .el-form-item.repeater-type-form-item,
      .el-form-item.lngLat-form-item {
        .el-form-item__content {
          display: flex;
          flex-wrap: nowrap;
        }
      }

      .el-form-item.traditional-dmr-allow-net-call .el-form-item__content {
        display: flex !important;
        flex-wrap: wrap;
      }

      .select-btn-box {
        display: flex;

        &.has-button .el-select .el-select__wrapper {
          border-top-right-radius: 0;
          border-bottom-right-radius: 0;
        }

        .el-button {
          border-top-left-radius: 0;
          border-bottom-left-radius: 0;
          height: 32px;
        }
      }
    }
  }

  .data-form-editor.page-controller {
    .ctrlStats_connect {
      background-color: #0ccf0c;
    }

    .ctrlStats_disConnect {
      background-color: #9ea7b4;
    }

    .ctrlStats_alarm {
      background-color: #f00;
    }

    .ctrlStats {
      display: inline-block;
      border-radius: 50%;
      width: 14px;
      height: 14px;
    }

    .repeater-monitor-ctrl {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 0.25rem;

      .el-button.show-repeater-status-btn {
        padding: 4px;
      }
    }
  }

  .sip-info {
    display: flex;
    padding: 8px 16px;

    .sip-info-item {
      &:not(:first-child) {
        margin-left: 32px;
      }

      &.hidden {
        display: none;
      }
    }
  }

  .get_lngLat_btns {
    width: 100%;
  }

  .bind-telephone-gateway-popover {
    flex: unset;
  }

  .controller-gateway-popover-footer {
    margin-top: 6px;
  }

  .controller-manager .data_btns {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
  }

  .controller-manager .data_btns .el-button {
    flex: auto;
    flex-grow: 0;
  }

  .controller-manager .el-form-item__content,
  .controller-manager .el-form-item__content .el-checkbox__label {
    line-height: 41px;
  }

  .row-details.simulcast {
    th,
    td {
      min-width: 100px;

      &.index,
      &.action {
        width: 100px;
      }

      &.name {
        max-width: 300px;
      }
    }

    .simulcast-repeater-action {
      outline: none;
      border: none;
      background-color: rgb(156, 39, 176);
      color: #fff;
      margin: 6px 0;

      &:hover,
      &:focus {
        background-color: rgba(156, 39, 176, 0.8);
      }
    }

    /* 统一 label 高度 */
    .el-form-item__label {
      height: 50px;
      line-height: 50px;
      display: flex;
      align-items: center;
    }
  }
</style>
