.maplibregl-popup[class*="maplibregl-popup-anchor-"] {
  .maplibregl-popup-tip {
    display: none;
  }
  .maplibregl-popup-content {
    width: 360px;
    height: auto;
    min-height: 150px;
    background-image: url('@/assets/images/dispatch/map/map-popup.png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;
    background-color: transparent;
    box-shadow: unset;
    display: flex;
    flex-direction: column;
    .popup-wrapper {
      margin-top: 28px;
      color: #fff;
      background: linear-gradient(to bottom, #1482CD70 0%, #1685CD29 100%);
      padding: 10px;
      margin-left: 4px;
      flex: 1;

      .popup_title {
        font-family: 'YouSheBiaoTiHei';
        font-size: 18px;
        position: absolute;
        top: -26px;
        left: 0;
        max-width: 100px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .close_icon {
        width: 32px;
        height: 32px;
        position: absolute;
        top: -44px;
        right: 0;
        background-image: url('@/components/bfDialog/images/close.png');
        background-size: 100% 100%;
        cursor: pointer;
      }

      .popup-body {
        .popup-item-label {
          font-family: 'AlibabaPuHuiTi2';
          font-size: 12px;
        }
        .popup-item-value {
          font-family: 'YouSheBiaoTiHei';
          font-size: 14px;
          color: #FF811D;
        }
        .popup-active-device-list, .popup-active-device-item {
          background-color: transparent;
        }

        .popup-footer {
          text-align: center;
        }
      }
    }
    p {
      text-align: left;
    }
  }
}