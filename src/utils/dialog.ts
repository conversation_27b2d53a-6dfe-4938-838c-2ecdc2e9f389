import { ref, h, createApp, type Component } from 'vue'
import bfDialog from '@/components/bfDialog/main'
import type { BfDialogProps } from '@/components/bfDialog/main'
import i18n from '@/modules/i18n'

//  打开对话框，返回一个 Promise<boolean>，表示对话框是否打开
const openDialog = (component: Component, props: Omit<BfDialogProps, 'modelValue'>): Promise<boolean> => {
  return new Promise(resolve => {
    const visible = ref<boolean>(true)

    const onClose = () => {
      props.onClose?.()
      visible.value = false
      resolve(false)
      app.unmount()
      container.remove()
    }

    // 判断组件是否需要包装在 bfDialog 中
    // systemVersion 现在自带 bfDialog，不需要额外包装
    const needsDialog = false // componentName === 'systemVersion'

    const Wrapper = {
      setup() {
        return () => {
          if (!visible.value) return null

          if (needsDialog) {
            // 需要包装在 bfDialog 中的组件
            return h(
              bfDialog,
              {
                modelValue: visible.value,
                'onUpdate:modelValue': (val: boolean) => (visible.value = val),
                title: props.title ?? '',
                closeOnClickModal: false,
                closeOnPressEscape: false,
                appendToBody: true,
                center: true,
                class: 'setting-dialog',
                modalClass: 'dialog-modal-mask',
                onClose,
              },
              {
                default: () => h(component, props),
              }
            )
          } else {
            // 自带弹窗的组件（如 authorization、systemVersion）
            // 使用 dialogVisible 避免与 vueMixin 中的 visible 冲突
            return h(component, {
              ...props,
              dialogVisible: visible.value,
              'onUpdate:dialogVisible': val => (visible.value = val),
            })
          }
        }
      },
    }

    const container = document.createElement('div')
    document.body.appendChild(container)
    const app = createApp(Wrapper)

    // 为应用提供完整的国际化支持
    app.use(i18n)

    app.mount(container)
  })
}

export default openDialog
