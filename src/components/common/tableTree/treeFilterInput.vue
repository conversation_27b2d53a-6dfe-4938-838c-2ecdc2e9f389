<script setup lang="ts">
  const { showIcon, isInPageHeader } = withDefaults(
    defineProps<{
      showIcon: boolean
      isInPageHeader: boolean
    }>(),
    {
      showIcon: false,
      isInPageHeader: false,
    }
  )

  const model = defineModel<string>()

  const emit = defineEmits<{
    (e: 'filter', value: string): void
  }>()
</script>

<template>
  <el-input
    ref="filterInput"
    v-model="model"
    :placeholder="$t('tree.filter')"
    :prefix-icon="showIcon ? 'search' : ''"
    :class="['table-tree-filter-input', { 'in-page-header': isInPageHeader }]"
    @input="emit('filter', model)"
    @keydown.enter="emit('filter', model)"
  />
</template>

<style lang="scss">
  .table-tree-filter-input {
    justify-content: center;
    .el-input__wrapper {
      background: linear-gradient(to right, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0));
      box-shadow: none;
      padding: 0;
      font-size: 16px;

      .el-input__prefix {
        color: #fff;
        line-height: 24px;
      }

      .el-input__inner,
      .el-input__inner::placeholder {
        text-align: center;
        color: #fff;
        line-height: 24px;
      }
    }

    &.in-page-header {
      .el-input__wrapper {
        background: transparent;
        height: 32px;
        font-size: 24px;
        font-weight: bold;

        .el-input__prefix {
          .el-icon {
            width: 24px;
            height: 24px;
          }
          color: #faffff;
        }

        .el-input__inner,
        .el-input__inner::placeholder {
          text-align: left;
          height: 32px;
          line-height: 32px;
          color: #faffff;
        }
      }
    }
  }
</style>
