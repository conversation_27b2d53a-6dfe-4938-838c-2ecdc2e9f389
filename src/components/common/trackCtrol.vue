<template>
  <div class="trackCtrol">
    <el-button-group>
      <el-tooltip popper-class="bf-tooltip" effect="dark" :content="$t('map.slowDown')" placement="top" :enterable="false">
        <el-button type="primary" icon="d-arrow-left" :disabled="slowest" @click="fastForward(false)" />
      </el-tooltip>
      <el-tooltip popper-class="bf-tooltip" effect="dark" :content="$t('map.fastForward')" placement="top" :enterable="false">
        <el-button type="primary" icon="d-arrow-right" :disabled="fastest" @click="fastForward(true)" />
      </el-tooltip>
      <el-tooltip popper-class="bf-tooltip" effect="dark" :content="playTitle" placement="top" :enterable="false">
        <el-button type="primary" :icon="isplay ? 'd-caret' : 'caret-right'" @click="playAnimate" />
      </el-tooltip>
      <el-tooltip popper-class="bf-tooltip" effect="dark" :content="$t('map.stop')" placement="top" :enterable="false">
        <el-button type="primary" icon="circle-close" @click="stopAnimate" />
      </el-tooltip>
    </el-button-group>
    <el-slider v-model="sliderValue" :max="sliderMax" :min="1" />
  </div>
</template>

<script>
  export default {
    name: 'TrackCtrol',
    emits: ['timeout', 'play', 'stop', 'sliderChange'],
    props: {
      sliderMax: {
        type: Number,
        default: 1,
      },
      end: Boolean,
      sliderVal: Number,
    },
    data() {
      return {
        sliderValue: 1,
        isplay: false,
        timeout: 1000,
      }
    },
    methods: {
      fastForward(isFast) {
        if (isFast) {
          if (this.timeout > 100) {
            this.timeout -= 100
          }
        } else {
          if (this.timeout < 2000) {
            this.timeout += 100
          }
        }
        this.$emit('timeout', this.timeout)
      },
      playAnimate() {
        this.isplay = !this.isplay
        this.$emit('play', this.isplay)
      },
      stopAnimate() {
        this.$emit('stop')
      },
    },
    watch: {
      sliderValue(val) {
        // 发布slider值更新消息，同步地图图层popup等信息
        this.$emit('sliderChange', val - 1)
      },
      end(val) {
        // 监听动画播放结束
        if (val) {
          this.sliderValue = 1
          this.isplay = false
        }
      },
      sliderVal(val) {
        // 接收新的slider值
        this.sliderValue = ++val
      },
    },
    mounted() {},
    computed: {
      playTitle() {
        return this.isplay ? this.$t('map.pased') : this.$t('map.play')
      },
      fastest() {
        return this.timeout === 100
      },
      slowest() {
        return this.timeout === 2000
      },
    },
  }
</script>

<style>
  .trackCtrol {
    position: fixed;
    top: 75px;
    left: 10px;
    max-width: 200px;
    z-index: 1000;
  }

  .trackCtrol button {
    line-height: 14px;
  }

  .trackCtrol .el-slider__runway {
    background-color: #fff;
  }
</style>
