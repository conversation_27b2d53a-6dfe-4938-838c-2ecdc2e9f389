<template>
  <el-main class="data-form-editor p-0">
    <dataTablesVue3
      ref="dataTable"
      :key="tableName"
      :detailHead="detailHead"
      :detailBodyName="detailBodyName"
      :detail-render="detailRender"
      :detailBodyIndex="detailBodyIndex"
      :head="customColumn"
      :data="data"
      :name="tableName"
      :exportNamePrefix="title"
      :scrollY="scrollY"
      :tableClass="tableClass"
      @row-dbclick="tableRowDblclick"
      @row-click="tableRowClick"
    >
      <template #top-left-after>
        <DataTableRowItem iconFont="bfdx-tianjia" :enable="true" @click="() => addNewData()">
          <ellipsis-text class="mb-0" :content="$t('dialog.add')" />
        </DataTableRowItem>
      </template>
    </dataTablesVue3>

    <bf-dialog
      ref="formEditorDialog"
      v-model="dialogVisible"
      append-to-body
      :class="dialogCustomClass"
      :width="editorWidth"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="showClose"
      :title="dialogTitle"
      :top="top"
      center
    >
      <template #header="{ titleId, titleClass }" v-if="!showClose">
        <div class="w-full flex justify-between items-center">
          <span :id="titleId" :class="titleClass" class="text-xl">
            {{ dialogTitle }}
          </span>
          <button class="bf-dialog-close-btn" @click="closeDialog">
            <span class="bf-dialog-close-icon"></span>
          </button>
        </div>
      </template>
      <slot name="form" :formData="formData" :isNewStatus="isNewStatus" />

      <template v-if="!noFormFooter" #footer>
        <slot name="form-footer" :onClose="closeDialog" :onConfirm="onConfirm" :isNewStatus="isNewStatus" :formData="formData">
          <div class="flex justify-center gap-3 dialog-footer">
            <BfBtn color-type="info" @click="closeDialog">
              {{ $t('dialog.back') }}
            </BfBtn>
            <BfBtn color-type="warning" @click="onConfirm(isNewStatus)">
              {{ $t('dialog.confirm') }}
            </BfBtn>
          </div>
        </slot>
      </template>
    </bf-dialog>

    <slot :formData="formData" :isNewStatus="isNewStatus" />
  </el-main>
</template>

<script>
  /**
   * 数据表格一行数据的通用类型
   * @typedef {Record<string, any>} DataRow
   * @property {string} rid
   */

  import { warningBox } from '@/utils/notify'
  import { notEditDataPermission } from '@/utils/bfutil'
  import { cloneDeep } from 'lodash'
  import bfproto from '@/modules/protocol'
  import { defineAsyncComponent } from 'vue'
  import editIcon from '@/assets/images/manage/edit_icon.svg'
  import deleteIcon from '@/assets/images/manage/del_icon.svg'
  import { calcScaleSize, convertPxToRemWithUnit } from '@/utils/setRem'
  import bfDialog from '@/components/bfDialog/main'
  import BfBtn from '@/components/bfButton/main'
  /**
   * 定义一个枚举对象
   * @param {Record<string, number|string>} obj
   * @returns {object} 只读的枚举对象
   */
  function defineEnum(obj) {
    const _proto_ = Object.create(null)
    const valueObj = Object.create(_proto_)
    const keys = Object.keys(obj)
    keys.forEach(key => {
      _proto_[(valueObj[key] = obj[key])] = key
    })
    return Object.freeze(valueObj)
  }

  /**
   * 数据编辑状态类型
   * @readonly
   * @enum {number} EditStatus
   */
  export const EditStatus = defineEnum({
    None: 0,
    Add: 1,
    Edit: 2,
    Delete: 3,
  })

  export default {
    name: 'DataFormEditor',
    emits: ['row-dblclick', 'row-click', 'close', 'row-new', 'row-update', 'row-delete', 'status-change'],
    components: {
      dataTablesVue3: defineAsyncComponent(() => import('@/components/common/dataTablesVue3.vue')),
      bfDialog,
      BfBtn,
    },
    props: {
      /**
       * 读取el-form表单元素实例，以便调用validate方法进行表单验证
       * @type {() => Vue.Ref}
       */
      getFormRef: {
        type: Function,
        required: true,
      },
      tableClass: {
        type: String,
        default: '',
      },
      title: {
        type: String,
        default: 'Unknown',
      },
      tableName: {
        type: String,
        default: 'Unknown',
      },
      /** @type {Array<DataRow>} */
      data: {
        type: Array,
        required: true,
      },
      /** @type {Array<import('datatables.net').ConfigColumns>} */
      column: {
        type: Array,
        required: true,
      },
      /** @type {Array<import('datatables.net').ConfigColumns>} */
      detailHead: {
        type: Array,
      },
      detailBodyName: {
        type: String,
      },
      detailBodyIndex: {
        type: Boolean,
        default: true,
      },
      detailRender: {
        type: Function,
      },
      getDetailData: {
        type: Function,
      },
      /**
       * 获取一行新的数据
       * @returns {DataRow}
       */
      getNewData: {
        type: Function,
        required: true,
      },
      /**
       * 再次确认删除方法，返回Promise
       * @type {confirmDeleteAgain}
       * @param {DataRow} data
       * @returns {Promise<boolean>}
       */
      confirmDeleteAgain: {
        type: Function,
      },
      /**
       * 编辑对话框确定按钮的前置事件，返回Promise
       * @type {beforeConfirm}
       * @param {DataRow} data
       * @param {EditStatus} status
       * @returns {Promise<boolean>}
       */
      beforeConfirm: {
        type: Function,
        default: () => Promise.resolve(true),
      },
      /**
       * 检查操作的权限，返回Promise
       * @type {beforeAction | undefined}
       * @param {EditStatus} status
       * @param {DataRow?} data
       * @returns {Promise<boolean>}
       */
      beforeAction: {
        type: Function,
      },
      /**
       * beforeAction之后执行，主要用于解析当前要修改的数据，返回Promise
       * 比如将setting中的数据解析出来，赋值到当前编辑的数据上
       * @type {beforeAction | undefined}
       * @param {DataRow?} data
       * @returns {Promise<boolean>}
       */
      parseDataForEdit: {
        type: Function,
      },
      /**
       * 关闭编辑对话的前置方法，接收一个回调，以完成关闭窗口
       * @type {beforeClose | undefined}
       * @param {() => void} done
       * @param {EditStatus} status
       */
      beforeClose: {
        type: Function,
      },
      /**
       * 判断一行数据是否被禁用操作列
       * @type {checkDataDisable}
       * @param {DataRow} row
       * @param {Record<string, any>} meta
       * @returns {boolean}
       */
      checkDataDisable: {
        type: Function,
        // prettier-ignore

        default: (_row, _meta) => false,
      },
      // 没有内置的表单操作页脚，父组件的表单内包含了操作按钮时使用
      noFormFooter: {
        type: Boolean,
        default: false,
      },
      editorClass: {
        type: String,
        default: '',
      },
      editorWidth: {
        type: String,
      },
      showClose: {
        type: Boolean,
        default: false,
      },
      top: {
        type: String,
        default: '15vh',
      },
    },
    data() {
      return {
        dialogVisible: false,
        /**
         * @type {EditStatus}
         */
        editStatus: EditStatus.None,
        /**
         * @type DataRow
         */
        formData: {},
        // 计算表格滚动高度，导航header高度146.5px，路由页面下边距47px，page-header高度35px
        // gap 5px, 自定义按钮区高度35px，上下边距共30px，自定义表尾高度35px
        // 滚动区域表头高度40px
        // 计算表格滚动高度- 146.5px - 47px - 35px - 5px - 35px - 30px - 38px - 40px =-376.5px
        scrollY: `calc(100vh - ${convertPxToRemWithUnit(calcScaleSize(376.5))})`,
      }
    },
    methods: {
      /**
       * 表格行双击事件
       * @param {DataRow} row
       * @param {jQuery.Event} e
       */
      tableRowDblclick(row, e) {
        this.$emit('row-dblclick', row, e)
      },
      /**
       * 表格行点击事件
       * @param {DataRow} row
       * @param {jQuery.Event} e
       */
      tableRowClick(row, e) {
        const newData = this.getNewData()
        const oldData = this.formData
        //  判断是否已打开添加或编辑页面
        if (this.editStatus === EditStatus.Edit || this.editStatus === EditStatus.Add) {
          // 保留正在编辑的数据，不使用更新的行数据
          this.formData = Object.assign({}, bfproto.copyFieldsFromProto(row), oldData)
        } else {
          this.formData = Object.assign({}, newData, bfproto.copyFieldsFromProto(row))
        }
        // 点击了编辑按钮
        if (e.target.classList.contains('data-action-btn') && !e.target.classList.contains('is-disabled')) {
          if (e.target.classList.contains('data-edit')) {
            this.onEdit()
          } else if (e.target.classList.contains('data-delete')) {
            // 点击了删除按钮
            this.onDelete()
          }
        }

        this.$emit('row-click', this.formData, e)
      },
      /**
       * 添加一行新数据, 父组件调用时可能需要对当前表单数据修改，传递一个对象进行合并
       * @param {object|null} parentData
       */
      async addNewData(parentData = null) {
        const formData = cloneDeep(this.getNewData())
        const isNext = await this.defaultBeforeAction(EditStatus.Add, formData)
        if (!isNext) return
        this.formData = formData
        Object.assign(this.formData, parentData)

        this.editStatus = EditStatus.Add
        this.dialogVisible = true
      },
      /**
       * 清除表单检验结果
       * @param {object|undefined} formIns
       */
      clearValidate(formIns) {
        formIns = formIns ?? this.getFormRef()
        // formIns?.resetFields()
        formIns?.clearValidate()
      },
      // 关闭编辑对话框
      closeDialog() {
        if (this.beforeClose) {
          this.beforeClose(() => this.onClose(), this.editStatus)
        } else {
          this.onClose()
        }
      },
      onClose() {
        this.dialogVisible = false
        this.clearValidate()
        this.resetEditStatus()
        this.$emit('close')
      },
      resetEditStatus() {
        this.editStatus = EditStatus.None
      },
      // 确定按钮点击事件
      async onConfirm(keepAdding = false) {
        const formIns = this.getFormRef()
        // 表单检验
        const isValid = await formIns?.validate().catch(err => {
          bfglob.console.warn('form validation failed!', err)
          return false
        })
        if (!isValid) return

        // 执行前置逻辑，比如检查权限，处理特殊字段参数等
        const isNext = await this.beforeConfirm(this.formData, this.editStatus).catch(err => {
          bfglob.console.warn('user reject confirm', err)
          return false
        })
        if (!isNext) return

        // 判断执行的添加还是编辑的逻辑
        if (this.editStatus === EditStatus.Add) {
          this.$emit('row-new', this.formData, this.closeDialog, keepAdding ? this.addNewData : undefined)
        } else if (this.editStatus === EditStatus.Edit) {
          this.$emit('row-update', this.formData, this.closeDialog)
        }
      },
      // 操作列删除按钮点击事件
      async onDelete() {
        let isNext = await this.defaultBeforeAction(EditStatus.Delete, this.formData)
        if (!isNext) return

        // 二次确定删除
        if (this.confirmDeleteAgain) {
          isNext = await this.confirmDeleteAgain(this.formData).catch(err => {
            bfglob.console.warn('confirmDeleteAgain catch', err)
            return false
          })
        } else {
          isNext = await warningBox(this.$t('dialog.deletePrompt')).catch(err => {
            bfglob.console.warn('user cancel delete', err)
            return false
          })
        }

        if (!isNext) return

        // 允许删除，向父组件传递事件
        this.editStatus = EditStatus.Delete
        this.$emit('row-delete', this.formData, this.resetEditStatus)
      },
      // 操作列编辑按钮点击事件
      async onEdit(parentData = null) {
        const isNext = await this.defaultBeforeAction(EditStatus.Edit, this.formData)
        if (!isNext) return

        Object.assign(this.formData, parentData)
        // 编辑数据时执行
        await this.parseDataForEdit?.(this.formData)

        // 打开并更新对话框状态
        this.dialogVisible = true
        this.editStatus = EditStatus.Edit
      },
      /**
       * 默认的检查权限方法，允许或拒绝编辑和添加新数据
       * @returns {Promise<boolean>}
       */
      async defaultCheckEditPermission() {
        // 没有权限
        if (notEditDataPermission()) {
          return Promise.reject('No edit permission')
        }

        return Promise.resolve(true)
      },
      /**
       * 添加、更新和删除操作的前置检查工作,优先调用传递的方法
       * @param {EditStatus} status
       * @param {DataRow?} row
       * @returns {Promise<boolean>}
       */
      async defaultBeforeAction(status, row) {
        // 调用传递进来的方法
        if (this.beforeAction) {
          return await this.beforeAction(status, row).catch(err => {
            bfglob.console.warn('beforeAction reject delete', err)
            return false
          })
        }
        // 默认的检查方法
        return await this.defaultCheckEditPermission().catch(err => {
          bfglob.console.warn('default beforeAction reject edit', err)
          return false
        })
      },
    },
    computed: {
      dialogCustomClass() {
        return ['data-form-dialog', this.editorClass].join(' ')
      },
      isNewStatus() {
        return this.editStatus === EditStatus.Add
      },
      customColumn() {
        return [
          ...this.column,
          // 操作列
          {
            title: this.$t('dataTable.actions'),
            data: null,
            width: '100px',
            orderable: false,
            class: 'actions',
            render: (data, type, row, meta) => {
              const isDisabled = this.checkDataDisable(row, meta)
              const disableCls = isDisabled ? 'is-disabled' : ''
              return `<div class="action-inner flex items-center justify-center gap-[35px] cursor-pointer"> <img src="${editIcon}" class="relative size-[20px]  ${disableCls} aspect-square data-action-btn data-edit" />
      <button type="button" class="${disableCls} data-action-btn data-delete"><img  src="${deleteIcon}" class="relative size-[20px]  ${disableCls} aspect-square data-action-btn data-delete" /></button></div>`
            },
          },
        ]
      },
      dialogTitle() {
        return this.editStatus === EditStatus.Add ? this.$t('dialog.add') : this.$t('dialog.edit')
      },
      dataTable() {
        return this.$refs.dataTable
      },
    },
    watch: {
      editStatus(status) {
        this.$emit('status-change', status)
      },
      data(value) {
        this.$refs.dataTable?.replaceTableData(value)
      },
    },
  }
</script>

<style lang="scss">
  .el-dialog.data-form-dialog {
    width: fit-content;

    &:not(.is-fullscreen) .el-dialog__body {
      // width: 800px;
      padding: 0 50px;
      display: flex;
      justify-content: center;
    }

    // .el-dialog__footer {
    // border-top: 1px solid #ddd;
    // }

    .el-form .el-form-item {
      margin-bottom: 16px;

      .el-form-item__error {
        padding-top: 2px;
        font-size: 10px;
      }
    }
  }

  .data-form-editor {
    flex: 0 1 auto;
    display: flex;
    flex-direction: column;
    border-image: linear-gradient(134.33deg, #9ca6d6, #7a88cb) 30 / 1px;
    border-width: 1px;
    border-style: solid;
    background: rgba(0, 0, 11, 0.3);
    @media (max-width: 1920px) {
      // 确保边框线不会小于1px
      /* prettier-ignore */
      border-image-width: 1Px;
    }
  }

  // 自定义关闭按钮样式，与 bfDialog 内置样式保持一致
  .bf-dialog-close-btn {
    position: absolute;
    top: -40px;
    right: 48px;
    width: 52px;
    height: 52px;
    border: none;
    background: transparent;
    cursor: pointer;
    padding: 0;

    .bf-dialog-close-icon {
      display: block;
      width: 100%;
      height: 100%;
      background-image: url('@/assets/images/common/close.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }

    &:hover {
      opacity: 0.8;
    }
  }
</style>
