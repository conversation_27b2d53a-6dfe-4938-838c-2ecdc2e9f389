<template>
  <div class="device-tree-wrapper" :style="deviceTreeStyle">
    <TableTree ref="bfDeviceTree" :treeId="treeId" :contextmenuOption="contextmenuOption" @select="selectNodes" @dblclick="dblclickNode" @loaded="treeLoaded" />
  </div>
</template>

<script>
  import TableTree from '@/components/common/tableTree'
  import bfprocess from '@/utils/bfprocess'
  import bftree from '@/utils/bftree'
  import bfutil, { DeviceTypes, getDeviceMapLonLat } from '@/utils/bfutil'
  import dynamicGroupMixin from '@/utils/dynamicGroup/dynamicGroupMixin'
  import maputil, { mapFlyTo, updateOrgMarkerStatus } from '@/utils/map'
  import { useRouteParams } from '@/router'

  // 支持查看设备状态的终端类型
  const SupportStatusDeviceTypes = [
    DeviceTypes.Device,
    DeviceTypes.Mobile,
    DeviceTypes.VirtualClusterDevice,
    DeviceTypes.PocDevice,
    DeviceTypes.VirtualRepeater,
  ]
  const { setRouteParams } = useRouteParams()

  export default {
    mixins: [dynamicGroupMixin],
    props: {
      treeId: {
        type: String,
        default: 'bfdx-tree-' + Date.now(),
      },
    },
    data() {
      return {
        isHideTree: false,
        deviceTreeWidth: 300,
      }
    },
    methods: {
      treeLoaded() {
        bfglob.treeLoaded = true
      },
      selectNodes(event, data) {
        const orgKeys = []
        const deviceKeys = []
        const nodes = data.tree.getSelectedNodes()
        for (let i = 0; i < nodes.length; i++) {
          const node = nodes[i]
          if (node.data?.isOrg) {
            orgKeys.push(node.key)
          } else {
            deviceKeys.push(node.key)
          }
        }
        this.show_org_marker_by_select_node(orgKeys)
        this.show_device_marker_by_select_node(deviceKeys)
      },
      dblclickNode(event, data) {
        if (!data.node) {
          return
        }
        // data.node.setSelected(true)

        if (data.node.data.isOrg) {
          const org = bfglob.gorgData.get(data.node.key)
          if (!org) {
            return
          }
          // use relative org marker lonlat
          const lonlatInfo = bfglob.gorgData.getOrgMapMakerPointLonlatInfo(org.rid)
          if (!lonlatInfo.lonlat) {
            return
          }
          mapFlyTo(lonlatInfo.lonlat, lonlatInfo.showLevel)
        } else {
          const device = bfglob.gdevices.get(data.node.key)
          if (!device) {
            return
          }
          mapFlyTo(getDeviceMapLonLat(device))
        }
      },
      emitMapResize() {
        this.$nextTick(() => {
          bfglob.emit('mapResize')
        })
      },
      showTree() {
        this.isHideTree = false
        this.deviceTreeWidth = 300
        this.emitMapResize()
      },
      hideTree() {
        this.isHideTree = true
        this.deviceTreeWidth = 0
        this.emitMapResize()
      },
      toggleTree() {
        if (this.isHideTree) {
          this.showTree()
        } else {
          this.hideTree()
        }
      },
      click_orgTree_channel(rid) {
        const device = bfglob.gdevices.get(rid)
        if (typeof device === 'undefined') {
          return
        }

        bfglob.emit('create_device_status_table', device)
      },
      openVsendcmdWindowRadio(target, radio, targetName) {
        setRouteParams('sendcmd', {
          isSelect: true,
          radio,
          target,
          targetName,
        })
        this.$router.push({
          name: 'sendcmd',
        })
      },
      show_device_marker_by_select_node(selKeys) {
        $('.devMarker')?.addClass('hide')
        for (const k in selKeys) {
          maputil.updateDeviceMarkerStatus(bfglob.gdevices.get(selKeys[k]))
        }
      },
      show_org_marker_by_select_node(selKeys) {
        // 先屏蔽所有的单位地图标记点
        const markers = bfglob.gmapPoints.getMarkerAll()
        for (const mk in markers) {
          // 跳过不是单位的地图标记眯,key='x'+rid
          const rid = mk.slice(1)
          if (!bfglob.gorgData.get(rid)) {
            continue
          }
          const marker = markers[mk]
          // marker.getElement()?.classList.add('hide')
          marker.hide(true)
        }
        // 根据选中的节点，进行标记点是否显示处理
        for (const k in selKeys) {
          updateOrgMarkerStatus(bfglob.gorgData.get(selKeys[k]))
        }
      },
      updateDeviceNodeTitle(device) {
        bftree.updateDeviceNodeTitle(bftree.defaultTreeId, device)
      },
      updateOneOrgNodeTitle(orgData) {
        bftree.updateOneOrgNodeTitle(bftree.defaultTreeId, orgData)
      },
      treeSortChildren() {
        bftree.sortChildren(this.treeId)
      },
    },
    computed: {
      deviceTreeStyle() {
        return {
          '--device-tree-width': `${this.deviceTreeWidth}px`,
        }
      },
      contextmenuOption() {
        const menu = [
          {
            title: this.$t('tree.collapseAll'),
            cmd: 'collapseAll',
          },
          {
            title: this.$t('tree.expandAll'),
            cmd: 'expandAll',
          },
          {
            title: this.$t('tree.online'),
            cmd: 'displayOnline',
          },
          {
            title: this.$t('tree.displayAllDev'),
            cmd: 'displayAll',
          },
        ]
        const cmdMenu = [
          {
            title: this.$t('dialog.locateCtrl'),
            cmd: 'cb01',
          },
          {
            title: this.$t('dialog.trailCtrl'),
            cmd: 'cb02',
          },
          {
            title: this.$t('dialog.telecontrol'),
            cmd: 'cb09',
          },
          {
            title: this.$t('tree.status'),
            cmd: 'stats',
          },
        ]
        const callMenu = [
          {
            title: this.$t('tree.quickCall'),
            cmd: 'quickCall',
          },
        ]
        const treeId = `#${this.treeId}`
        const showCommonMenu = () => {
          $(treeId).contextmenu('replaceMenu', [...callMenu, { title: '----' }, ...menu])
        }

        return {
          delegate: 'span.fancytree-node',
          autoFocus: true,
          menu: [],
          beforeOpen: (event, ui) => {
            const node = $.ui.fancytree.getNode(ui.target)
            if (!node) {
              return false
            }
            // Modify menu entries depending on node status
            // cmdMenu.forEach((item) => {
            //   $(treeId).contextmenu("enableEntry", item.cmd, !node.isFolder());
            // })
            // cmdMenu.forEach((item) => {
            //   $(treeId).contextmenu('showEntry', item.cmd, !node.isFolder())
            // })
            if (node.isFolder()) {
              showCommonMenu()
            } else {
              let hasStatus = false
              let key = node.key
              if (key.length > 36) {
                key = key.slice(0, 36)
              }
              const device = bfglob.gdevices.get(key)
              // 只有指定的设备才显示状态
              if (device && SupportStatusDeviceTypes.includes(device.deviceType)) {
                hasStatus = true
              }
              if (hasStatus) {
                $(treeId).contextmenu('replaceMenu', [...callMenu, ...cmdMenu, { title: '----' }, ...menu])
              } else {
                showCommonMenu()
              }
            }
            node.setActive()
            ui.extraData.node = node
          },
          close: (event, ui) => {
            // var node = $.ui.fancytree.getNode(ui.target);
            // node.setFocus();
          },
          select: (event, ui) => {
            try {
              const node = $.ui.fancytree.getNode(ui.target) || ui.extraData.node
              const target = node ? node.key : ''
              let device

              if (!node) {
                return
              }

              switch (ui.cmd) {
                case 'quickCall':
                  bfglob.emit('openMenuItem', 'command/bfSpeaking', vm => {
                    vm.speakFast(node.data.dmrId)
                  })
                  break
                case 'cb01':
                  if (bfutil.notSendCmdPermission()) {
                    return
                  }
                  if (bfglob.userInfo.isSuper) {
                    return
                  }
                  device = bfglob.gdevices.get(target)
                  if (!device) {
                    return
                  }
                  bfprocess.cb01(
                    { device: [device.dmrId] },
                    {
                      spaceTime: 5,
                      count: 1,
                      size: 20,
                    }
                  )
                  break
                case 'cb02':
                  if (bfglob.userInfo.isSuper) {
                    return
                  }
                  // 跳转到发送命令页面并选择跟踪监控radio和终端
                  this.openVsendcmdWindowRadio(target, 'cb02', ui.target[0].innerText)
                  break
                case 'cb09':
                  if (bfglob.userInfo.isSuper) {
                    return
                  }
                  this.openVsendcmdWindowRadio(target, 'cb09', ui.target[0].innerText)
                  break
                case 'stats':
                  const key = target.slice(0, 36)
                  if (node && !node.isFolder()) {
                    this.click_orgTree_channel(key)
                  }
                  break
                case 'collapseAll':
                  bftree.treeExpandAll(this.treeId, false)
                  break
                case 'expandAll':
                  bftree.treeExpandAll(this.treeId, true)
                  break
                case 'displayOnline':
                  this.$refs.bfDeviceTree?.showOnlineDevices()
                  break
                case 'displayAll':
                  this.$refs.bfDeviceTree?.showAllDevices()
                  break
              }
            } catch (e) {
              bfglob.console.error('contextmenu:', e)
            }
          },
        }
      },
    },
    components: {
      TableTree,
    },
    mounted() {
      // 切换列表树显示或隐藏功能
      bfglob.on(
        'toggleTree',
        function () {
          this.toggleTree()
        }.bind(this)
      )
      bfglob.on(
        'bflayout',
        function (level) {
          level > 0 ? this.showTree() : this.hideTree()
        }.bind(this)
      )
      bfglob.layout > 0 ? this.showTree() : this.hideTree()
      bfglob.on('vusers_table_update_data', data => {
        const devices = bfglob.gdevices.getAll()
        for (const i in devices) {
          const device = devices[i]
          if (device.lastRfidPerson === data.rid || device.deviceUser === data.rid) {
            this.updateDeviceNodeTitle(device)
            bfglob.emit('updateDeviceMarker', device)
          }
        }
      })

      // 监听重新过滤在线设备事件
      bfglob.on('updateDeviceNodeTitle', this.updateDeviceNodeTitle)
      bfglob.on('treeSortChildren', this.treeSortChildren)
      bfglob.on('updateOneOrgNodeTitle', this.updateOneOrgNodeTitle)
    },
    beforeUnmount() {
      bfglob.off('updateDeviceNodeTitle', this.updateDeviceNodeTitle)
      bfglob.off('treeSortChildren', this.treeSortChildren)
      bfglob.off('updateOneOrgNodeTitle', this.updateOneOrgNodeTitle)
    },
  }
</script>

<style lang="scss">
  .device-tree-wrapper {
    --device-tree-width: 300px;
    border-left: 2px solid #20a0ff;
    width: var(--device-tree-width);
  }

  .dynamic-group-detail-status {
    margin-left: 4px;
    font-style: italic;
    color: #909399;
  }

  .ui-contextmenu.ui-menu {
    text-align: left;
  }

  .ui-menu .ui-menu-item-wrapper {
    padding: 4px 1.5em 3px 1.5em;
  }

  .ui-contextmenu.ui-widget.ui-widget-content {
    font-size: 14px;
  }

  .device_channel {
    display: inline-block;
    color: #fff;
    border-radius: 8px;
    text-align: center;
    margin-left: 2px;
    background-color: #20a0ff;
    padding: 0 6px;
    line-height: 1.5;
  }

  .device_user_name {
    margin-left: 6px;
  }

  .allDevCount {
    display: inline-block;
    color: #fff;
    border-radius: 4px;
    text-align: center;
    padding: 2px 4px;
    line-height: 15px;
    margin: 0 5px;
    background-color: #9ea7b4;
  }

  .filterTreeInput {
    box-sizing: border-box;
    padding-left: 4px;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
  }

  .device_status {
    width: 54px;
    height: 76px;
    margin-right: 2px;
    position: relative;
    display: inline-block;
    vertical-align: sub;

    &_none {
      background-image: none;
    }

    &_yellow {
      background: url('@/assets/images/dispatch/map/yellow_cir.svg') no-repeat center center;
      background-size: cover;
    }

    &_light_gray {
      background: url('@/images/mapImg/light_gray_cir.png') no-repeat center center;
      background-size: cover;
    }

    &_gray {
      background: url('@/images/mapImg/gray_cir.png') no-repeat center center;
      background-size: cover;
    }

    &_green {
      background: url('@/assets/images/dispatch/map/green_cir.svg') no-repeat center center;
      background-size: cover;
    }

    &_red {
      background: url('@/assets/images/dispatch/map/red.svg') no-repeat center center;
      background-size: cover;
    }

    &_other_red {
      position: relative;

      &:after {
        content: '';
        display: block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        position: absolute;
        top: 4px;
        right: 4px;
        z-index: 10;
        background-color: red;
      }
    }

    &_icon_th {
      background: url('@/assets/images/dispatch/map/th.svg') no-repeat center center;
      background-size: cover;
      width: 16px;
      height: 16px;
      margin-top: 2px;
    }

    &_emergency_green {
      background: url('@/assets/images/dispatch/map/device_emergency_green.gif') no-repeat center center;
      background-size: cover;
    }

    &_emergency_yellow {
      background: url('@/assets/images/dispatch/map/device_emergency_yellow.gif') no-repeat center center;
      background-size: cover;
    }
  }

  @keyframes device_emergency_green {
    50% {
      background-image: radial-gradient(#fff 0, rgba(12, 207, 12, 1) 50%);
    }

    100% {
      background-image: radial-gradient(#fff 0, rgba(255, 0, 0, 1) 50%);
    }
  }

  @keyframes device_emergency_yellow {
    50% {
      background-image: radial-gradient(#fff 0, #ffeb0d 50%);
    }

    100% {
      background-image: radial-gradient(#fff 0, rgba(255, 0, 0, 1) 50%);
    }
  }
</style>
